# This workflow will trigger a Vercel deployment when a new tag is pushed.
#
# You need to add the following secrets to your GitHub repository:
# - VERCEL_ORG_ID: Your Vercel organization ID.
# - VERCEL_PROJECT_ID: Your Vercel project ID.
# - VERCEL_TOKEN: Your Vercel authentication token.
#
# You can find your VERCEL_ORG_ID and VERCEL_PROJECT_ID in the .vercel/project.json
# file after linking your project with `vercel link`.
# You can create a Vercel token at https://vercel.com/account/tokens

name: Vercel Production Deployment

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    tags:
      - '*' # Push events to tags like 1.0.0

jobs:
  Deploy-Production:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      # ✅ Install Bun so `bun.lockb` can be used
      - name: Install Bun
        run: |
          curl -fsSL https://bun.sh/install | bash
          echo "$HOME/.bun/bin" >> $GITHUB_PATH
          export PATH="$HOME/.bun/bin:$PATH"
          bun --version
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
