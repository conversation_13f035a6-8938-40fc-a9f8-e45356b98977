{"id": "fd2d34e8-303d-4347-844b-a528b605d9fd", "prevId": "bad06fbe-ef70-4867-ae5e-33d1202ba448", "version": "7", "dialect": "postgresql", "tables": {"public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_key_unique": {"name": "api_keys_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"project_id": {"name": "project_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "project_name": {"name": "project_name", "type": "text", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "cover_color": {"name": "cover_color", "type": "text", "primaryKey": false, "notNull": false}, "cover_pic": {"name": "cover_pic", "type": "text", "primaryKey": false, "notNull": false}, "orientation": {"name": "orientation", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "numeric(6, 3)", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "speech": {"name": "speech", "type": "jsonb", "primaryKey": false, "notNull": false}, "background_video": {"name": "background_video", "type": "jsonb", "primaryKey": false, "notNull": false}, "music": {"name": "music", "type": "jsonb", "primaryKey": false, "notNull": false}, "caption_settings": {"name": "caption_settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "scenes": {"name": "scenes", "type": "jsonb", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "text", "primaryKey": false, "notNull": false}, "run_id": {"name": "run_id", "type": "text", "primaryKey": false, "notNull": false}, "blog_images": {"name": "blog_images", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"idx_projects_user_id": {"name": "idx_projects_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}