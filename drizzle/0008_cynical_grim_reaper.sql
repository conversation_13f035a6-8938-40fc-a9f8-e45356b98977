CREATE TABLE "render_jobs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"project_id" text NOT NULL,
	"user_id" text NOT NULL,
	"status" text NOT NULL,
	"progress" integer DEFAULT 0,
	"public_url" text,
	"thumbnail_url" text,
	"error_message" text,
	"render_method" text,
	"export_name" text,
	"export_resolution" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
DROP INDEX "idx_stock_music_genre";--> statement-breakpoint
DROP INDEX "idx_stock_music_mood";--> statement-breakpoint
DROP INDEX "idx_stock_music_provider";--> statement-breakpoint
CREATE INDEX "idx_render_jobs_user_project" ON "render_jobs" USING btree ("user_id","project_id");--> statement-breakpoint
CREATE INDEX "idx_render_jobs_status" ON "render_jobs" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_render_jobs_created_at" ON "render_jobs" USING btree ("created_at");