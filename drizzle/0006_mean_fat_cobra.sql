CREATE TABLE "media_assets" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"file_name" text NOT NULL,
	"original_name" text NOT NULL,
	"mime_type" text NOT NULL,
	"file_size" integer NOT NULL,
	"original_url" text NOT NULL,
	"thumbnail_url" text,
	"low_res_url" text,
	"width" integer,
	"height" integer,
	"duration" numeric(10, 3),
	"quality" text,
	"fps" integer,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE INDEX "idx_media_assets_user_id" ON "media_assets" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_media_assets_mime_type" ON "media_assets" USING btree ("mime_type");--> statement-breakpoint
CREATE INDEX "idx_media_assets_created_at" ON "media_assets" USING btree ("created_at");