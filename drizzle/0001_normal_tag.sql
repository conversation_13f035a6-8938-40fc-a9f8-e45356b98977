CREATE TABLE "projects" (
	"project_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"project_name" text NOT NULL,
	"method" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"cover_color" text,
	"cover_pic" text,
	"orientation" text NOT NULL,
	"duration" numeric(6, 3),
	"summary"  text,
	"speech" jsonb,
	"background_video" jsonb,
	"music" jsonb,
	"caption_settings" jsonb,
	"scenes" jsonb
);
--> statement-breakpoint
CREATE INDEX "idx_projects_user_id" ON "projects" USING btree ("user_id");
