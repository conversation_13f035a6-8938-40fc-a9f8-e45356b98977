CREATE TABLE "stock_music" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" text NOT NULL,
	"genre" text NOT NULL,
	"mood" text NOT NULL,
	"artist_name" text NOT NULL,
	"artist_url" text,
	"provider" text NOT NULL,
	"license_id" text NOT NULL,
	"source_url" text,
	"preview_url" text NOT NULL,
	"duration_millis" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE INDEX "idx_stock_music_genre" ON "stock_music" USING btree ("genre");--> statement-breakpoint
CREATE INDEX "idx_stock_music_mood" ON "stock_music" USING btree ("mood");--> statement-breakpoint
CREATE INDEX "idx_stock_music_provider" ON "stock_music" USING btree ("provider");