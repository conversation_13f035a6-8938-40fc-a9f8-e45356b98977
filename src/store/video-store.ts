import { create } from 'zustand'
import {
  Scene,
  SubtitlePosition,
  SubtitleStyle,
  TransitionType,
} from '@/types/video'
import type { Caption } from '@/lib/types'
import { PlayerRef } from '@remotion/player'
import type { RefObject } from 'react'

export type VideoOrientation = 'landscape' | 'square' | 'portrait'

export interface MusicTrack {
  id: string
  title: string
  genre: string
  mood: string
  artistName: string
  artistUrl?: string
  provider: string
  licenseId: string
  sourceUrl?: string
  previewUrl: string
  durationMillis: number
}

// Define the history entry type
type HistoryEntry = {
  scenes: Scene[]
  currentScene: string
  subtitlePosition: SubtitlePosition
  orientation: VideoOrientation
  selectedMusic: MusicTrack | null
  musicVolume: number
  musicEnabled: boolean
  captionsEnabled: boolean
}

interface Music {
  enabled: boolean
  src: string
  volume: number
  duration: number
  name: string
}

interface Speech {
  enabled: boolean
  src: string
  name: string
  volume: number
  transcript: {
    captions: Caption[]
    status: string
  }
}

interface BackgroundVideo {
  src: string
  muted: boolean
}

export interface VideoProject {
  projectId: string
  userId: string
  projectName: string
  method: string
  createdAt: string
  updatedAt: string
  coverColor: string
  coverPic: string | null
  orientation: 'landscape' | 'portrait' | 'square'
  duration: number
  summary: string
  music: Music
  speech: Speech | null
  captionSettings: SubtitleStyle & { enabled: boolean }
  backgroundVideo: BackgroundVideo | null
  scenes: Scene[]
  blogImages?: string[]
}

// API Response interfaces
interface ApiSceneMedia {
  fit: string
  kenBurns: string
  position: { x: number; y: number }
  thumbnail: string
  size: { height: number; width: number }
  transition: { duration: number; type: string }
  type: 'Image' | 'Video'
  url: string
}

interface ApiVoiceSettings {
  voiceId: string
  voiceName: string
  voiceSpeed: number
  voiceUrl: string
  voiceVol: number
}

interface ApiCaption {
  end: number
  sentence: string
  start: number
  wordBoundries: Array<{
    end: number
    start: number
    word: string
  }>
}

interface ApiScene {
  captions: ApiCaption[]
  duration: number
  id: string
  media: ApiSceneMedia
  startOffset: number
  text: string
  title: string | null
  voiceSettings: ApiVoiceSettings
}

interface ApiVideoProject {
  backgroundVideo: { muted: boolean; src: string } | null
  captionSettings: {
    animation: string
    backgroundColor: string
    backgroundOpacity: number
    borderRadius: number
    enabled: boolean
    fontFamily: string
    fontSize: number
    fontStyle: string
    fontWeight: string
    highlightColor: string
    maxWidth: number
    padding: number
    textAlign: string
    textColor: string
    textShadow: boolean
  }
  coverColor: string
  coverPic: string | null
  createdAt: string
  duration: string
  method: string
  music: {
    duration: number
    enabled: boolean
    name: string
    src: string
    volume: number
  }
  orientation: 'landscape' | 'portrait' | 'square'
  projectId: string
  projectName: string
  scenes: ApiScene[]
  speech: {
    enabled: boolean
    src: string
    name: string
    volume: number
    transcript: {
      captions: Array<{
        start: number
        end: number
        sentence: string
        wordBoundries: Array<{ start: number; end: number; word: string }>
      }>
      status: string
    }
  } | null
  summary: string
  updatedAt: string
  userId: string
  blogImages?: string[]
}

// Transform API response to store format
const transformApiProjectToStore = (
  apiProject: ApiVideoProject
): VideoProject => {
  const transformedScenes: Scene[] = apiProject.scenes.map(apiScene => {
    // Check if this is a podcast flow (speech is not null)
    const isPodcastOrAudioFlow = !!apiProject.speech

    return {
      id: apiScene.id,
      name: apiScene.title || `Scene ${apiScene.id}`,
      duration: apiScene.duration,
      startOffset: apiScene.startOffset,
      originalDuration: isPodcastOrAudioFlow
        ? apiScene.duration
        : apiScene.duration * (apiScene.voiceSettings?.voiceSpeed || 1),
      text: apiScene.text,
      voiceSettings: isPodcastOrAudioFlow
        ? {
            voiceId: '',
            voiceUrl: '',
            voiceVol: 1,
            voiceName: '',
            voiceSpeed: 1,
          }
        : {
            voiceId: apiScene.voiceSettings.voiceId,
            voiceUrl: apiScene.voiceSettings.voiceUrl,
            voiceVol: apiScene.voiceSettings.voiceVol,
            voiceName: apiScene.voiceSettings.voiceName,
            voiceSpeed: apiScene.voiceSettings.voiceSpeed,
          },
      // Add voiceover for compatibility with Remotion components
      voiceover: isPodcastOrAudioFlow
        ? undefined
        : {
            audioUrl: apiScene.voiceSettings.voiceUrl,
            audioDuration: apiScene.duration,
            volume: apiScene.voiceSettings.voiceVol * 100, // Convert to percentage
            speed: apiScene.voiceSettings.voiceSpeed,
          },
      // Add captions for subtitle display
      captions:
        apiScene.captions?.map(caption => ({
          start: caption.start,
          end: caption.end,
          text: caption.sentence,
          words: (caption.wordBoundries || []).map(word => ({
            start: word.start,
            end: word.end,
            word: word.word,
          })),
        })) || [],
      texts: [],
      media: apiScene.media
        ? {
            id: `media-${apiScene.id}`,
            type: apiScene.media?.type?.toLowerCase() as 'image' | 'video',
            url: apiScene.media?.url,
            position: apiScene.media?.position,
            size: apiScene.media?.size,
            startTime: 0,
            endTime: apiScene.duration,
            thumbnail: apiScene.media?.thumbnail,
            duration: apiScene.duration,
            fit: (['blur', 'crop'].includes(apiScene.media?.fit)
              ? apiScene.media?.fit
              : 'blur') as 'blur' | 'crop',
            transition:
              (apiScene.media?.transition?.type?.toLowerCase() as TransitionType) ||
              'fade',
            kenBurns: ([
              'none',
              'zoom-in',
              'zoom-out',
              'pan-left',
              'pan-right',
              'pan-up',
              'pan-down',
            ].includes(apiScene.media?.kenBurns)
              ? apiScene.media?.kenBurns
              : 'zoom-in') as
              | 'none'
              | 'zoom-in'
              | 'zoom-out'
              | 'pan-left'
              | 'pan-right'
              | 'pan-up'
              | 'pan-down',
            effectDuration:
              typeof apiScene.media?.transition?.duration === 'number'
                ? apiScene.media?.transition?.duration
                : 2,
          }
        : undefined,
    }
  })

  return {
    projectId: apiProject.projectId,
    userId: apiProject.userId,
    projectName: apiProject.projectName,
    method: apiProject.method,
    createdAt: apiProject.createdAt,
    updatedAt: apiProject.updatedAt,
    coverColor: apiProject.coverColor,
    coverPic: apiProject.coverPic,
    orientation: apiProject.orientation,
    duration: parseFloat(apiProject.duration),
    summary: apiProject.summary,
    music: apiProject.music,
    speech: apiProject.speech,
    captionSettings: {
      id: 'api-style',
      name: 'API Style',
      isCustom: false,
      createdAt: new Date().toISOString(),
      ...apiProject.captionSettings,
      enabled: apiProject.captionSettings?.enabled ?? true,
    } as SubtitleStyle & { enabled: boolean },
    backgroundVideo: apiProject.backgroundVideo,
    scenes: transformedScenes,
    blogImages: apiProject.blogImages ?? [],
  }
}

interface VideoStudioState {
  // State
  scenes: Scene[]
  currentScene: string
  subtitlePosition: SubtitlePosition
  orientation: VideoOrientation
  isLoading: boolean
  captionsEnabled: boolean

  // Music state
  selectedMusic: MusicTrack | null
  musicVolume: number
  musicEnabled: boolean

  // History
  history: HistoryEntry[]
  historyIndex: number
  isHistoryMutating: boolean

  // Project
  project: VideoProject | null

  // Video player ref
  playerRef: RefObject<PlayerRef | null> | null

  // Actions
  addScene: (insertAtIndex?: number) => void
  updateScene: (sceneId: string, updates: Partial<Scene>) => void
  updateVoiceoverSpeed: (sceneId: string, newSpeed: number) => void
  setCurrentScene: (sceneId: string) => void
  setSubtitlePosition: (position: SubtitlePosition) => void
  setOrientation: (orientation: VideoOrientation) => void
  setLoading: (loading: boolean) => void
  deleteScene: (sceneId: string) => void
  duplicateScene: (sceneId: string) => void
  setCaptionsEnabled: (enabled: boolean) => void

  // Music actions
  setSelectedMusic: (music: MusicTrack | null) => void
  setMusicVolume: (volume: number) => void
  setMusicEnabled: (enabled: boolean) => void

  // History Actions
  undo: () => void
  redo: () => void
  canUndo: () => boolean
  canRedo: () => boolean

  // Video player actions
  setPlayerRef: (ref: RefObject<PlayerRef | null> | null) => void
  seekToTime: (timeInSeconds: number) => void

  // Utility Actions
  clearVideo: () => void

  // Private helpers - not exposed in type but used internally
  _addToHistory: () => void
  _addToHistoryDebounced: () => void
  _updateProjectDuration: () => void

  // Project actions
  setProjectData: (project: VideoProject | ApiVideoProject) => void
  updateProjectName: (name: string) => void
}

// Initial state
const initialState = {
  scenes: [
    {
      id: '1',
      name: 'Scene 1',
      duration: 5.75,
      originalDuration: 5.75,
      text: 'Welcome to Adori AI video creation. This powerful platform helps you transform your ideas into professional videos with AI-powered voiceover and dynamic visuals.',
      voiceSettings: {
        voiceId: '',
        voiceUrl: '',
        voiceVol: 1,
        voiceName: '',
        voiceSpeed: 1,
      },
      texts: [],
      media: {
        id: 'media-1',
        type: 'image',
        url: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg',
        position: { x: 0, y: 0 },
        size: { width: 1920, height: 1080 },
        startTime: 0,
        endTime: 5.75,
        thumbnail:
          'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=100',
        fit: 'contain',
        transition: 'fade',
        kenBurns: 'zoom-in',
        effectDuration: 1.5,
      },
    },
    {
      id: '2',
      name: 'Scene 2',
      duration: 4.2,
      originalDuration: 4.2,
      text: 'Create stunning videos from your blog posts, ideas, or any text content. Choose from a variety of voices and customize every aspect of your video.',
      voiceSettings: {
        voiceId: '',
        voiceUrl: '',
        voiceVol: 1,
        voiceName: '',
        voiceSpeed: 1,
      },
      texts: [],
      media: {
        id: 'media-2',
        type: 'image',
        url: 'https://images.pexels.com/photos/3183153/pexels-photo-3183153.jpeg',
        position: { x: 0, y: 0 },
        size: { width: 1920, height: 1080 },
        startTime: 0,
        endTime: 4.2,
        thumbnail:
          'https://images.pexels.com/photos/3183153/pexels-photo-3183153.jpeg?auto=compress&cs=tinysrgb&w=100',
        fit: 'contain',
        transition: 'slideright',
        kenBurns: 'zoom-in',
        effectDuration: 1.5,
      },
    },
  ] as Scene[],
  currentScene: '1',
  subtitlePosition: { x: 240, y: 246 },
  orientation: 'landscape' as VideoOrientation,
  project: null,
}

export const useVideoStore = create<VideoStudioState>((set, get) => ({
  // State
  ...initialState,
  isLoading: false,
  captionsEnabled: true,

  // Music state
  selectedMusic: null,
  musicVolume: 15,
  musicEnabled: true,

  // History
  history: [
    {
      scenes: JSON.parse(JSON.stringify(initialState.scenes)),
      currentScene: initialState.currentScene,
      subtitlePosition: { ...initialState.subtitlePosition },
      orientation: initialState.orientation,
      selectedMusic: null,
      musicVolume: 15,
      musicEnabled: true,
      captionsEnabled: true,
    },
  ],
  historyIndex: 0,
  isHistoryMutating: false,

  // Video player ref
  playerRef: null,

  // Helper to add to history
  _addToHistory: () => {
    const state = get()
    if (state.isHistoryMutating) return

    const newHistoryEntry: HistoryEntry = {
      scenes: JSON.parse(JSON.stringify(state.scenes)),
      currentScene: state.currentScene,
      subtitlePosition: { ...state.subtitlePosition },
      orientation: state.orientation,
      selectedMusic: state.selectedMusic,
      musicVolume: state.musicVolume,
      musicEnabled: state.musicEnabled,
      captionsEnabled: state.captionsEnabled,
    }

    const newHistory = state.history.slice(0, state.historyIndex + 1)
    newHistory.push(newHistoryEntry)

    set({
      history: newHistory,
      historyIndex: newHistory.length - 1,
    })
  },

  // Actions with history tracking
  addScene: (insertAtIndex?: number) =>
    set(state => {
      state._addToHistoryDebounced()

      // Check if this is a podcast flow
      const isPodcastOrAudioFlow = !!state.project?.speech

      // For podcast flows, check if we're inserting between existing scenes
      if (
        isPodcastOrAudioFlow &&
        insertAtIndex !== undefined &&
        insertAtIndex > 0 &&
        insertAtIndex < state.scenes.length
      ) {
        // Podcast flow: Insert between existing scenes by halving adjacent scenes
        const previousScene = state.scenes[insertAtIndex - 1]
        const nextScene = state.scenes[insertAtIndex]

        // Calculate new durations and offsets
        const previousSceneNewDuration = previousScene.duration / 2
        const nextSceneNewDuration = nextScene.duration / 2
        const nextSceneNewOffset =
          (nextScene.startOffset || 0) + nextSceneNewDuration

        // Create new scene
        const newScene: Scene = {
          id: (state.scenes.length + 1).toString(),
          name: `Scene ${state.scenes.length + 1}`,
          duration: previousSceneNewDuration + nextSceneNewDuration,
          originalDuration: previousSceneNewDuration + nextSceneNewDuration,
          startOffset:
            (previousScene.startOffset || 0) + previousSceneNewDuration,
          text: '',
          voiceSettings: {
            voiceId: '',
            voiceUrl: '',
            voiceVol: 1,
            voiceName: '',
            voiceSpeed: 1,
          },
          texts: [],
          media: {
            id: 'media-new',
            type: 'image',
            url: '',
            position: { x: 0, y: 0 },
            size: { width: 1920, height: 1080 },
            startTime: 0,
            endTime: previousSceneNewDuration + nextSceneNewDuration,
            thumbnail: '',
            fit: 'contain',
            transition: 'fade',
            kenBurns: 'zoom-in',
            effectDuration: 1.5,
          },
        }

        // Update existing scenes
        const updatedScenes = state.scenes.map((scene, index) => {
          if (index === insertAtIndex - 1) {
            // Update previous scene duration
            return { ...scene, duration: previousSceneNewDuration }
          } else if (index === insertAtIndex) {
            // Update next scene offset and halve its duration
            return {
              ...scene,
              startOffset: nextSceneNewOffset,
              duration: scene.duration / 2,
            }
          } else if (index > insertAtIndex) {
            // Renumber subsequent scenes
            return { ...scene, name: `Scene ${index + 2}` }
          }
          return scene
        })

        // Insert new scene
        const newScenes = [
          ...updatedScenes.slice(0, insertAtIndex),
          newScene,
          ...updatedScenes.slice(insertAtIndex),
        ]

        // Update total project duration
        setTimeout(() => get()._updateProjectDuration(), 0)

        return { scenes: newScenes, currentScene: newScene.id }
      } else {
        // Regular flow or adding at the end: Use original logic
        let startOffset = 0
        if (insertAtIndex !== undefined && insertAtIndex > 0) {
          // If inserting at a specific position, calculate offset from the scene before it
          const previousScene = state.scenes[insertAtIndex - 1]
          if (previousScene) {
            startOffset =
              (previousScene.startOffset || 0) + (previousScene.duration || 0)
          }
        } else if (state.scenes.length > 0) {
          // If adding at the end, calculate offset from the last scene
          const lastScene = state.scenes[state.scenes.length - 1]
          startOffset = (lastScene.startOffset || 0) + (lastScene.duration || 0)
        }

        const newScene: Scene = {
          id: (state.scenes.length + 1).toString(),
          name: `Scene ${state.scenes.length + 1}`,
          duration: 5,
          originalDuration: 5, // Set original duration for new scenes
          startOffset: startOffset, // Set the calculated start offset
          text: '',
          voiceSettings: {
            voiceId: '',
            voiceUrl: '',
            voiceVol: 1,
            voiceName: '',
            voiceSpeed: 1,
          },
          texts: [],
          media: {
            id: 'media-new',
            type: 'image',
            url: '',
            position: { x: 0, y: 0 },
            size: { width: 1920, height: 1080 },
            startTime: 0,
            endTime: 5,
            thumbnail: '',
            fit: 'contain',
            transition: 'fade',
            kenBurns: 'zoom-in',
            effectDuration: 1.5,
          },
        }

        const newScenes =
          insertAtIndex !== undefined &&
          insertAtIndex >= 0 &&
          insertAtIndex <= state.scenes.length
            ? [
                ...state.scenes.slice(0, insertAtIndex),
                newScene,
                ...state.scenes.slice(insertAtIndex),
              ]
            : [...state.scenes, newScene]

        // Update total project duration after adding scene
        setTimeout(() => get()._updateProjectDuration(), 0)

        return { scenes: newScenes, currentScene: newScene.id }
      }
    }),

  updateScene: (sceneId, updates) =>
    set(state => {
      state._addToHistoryDebounced()
      const newState = {
        scenes: state.scenes.map(scene => {
          if (scene.id === sceneId) {
            const updatedScene = { ...scene, ...updates }

            // If duration is manually updated, update originalDuration too
            // (unless it's from a speed change which already handles originalDuration)
            if (updates.duration !== undefined && !updates.originalDuration) {
              updatedScene.originalDuration = updates.duration
              // Reset voice speed to 1 if duration was manually changed
              if (updatedScene.voiceSettings) {
                updatedScene.voiceSettings.voiceSpeed = 1
              }
            }

            return updatedScene
          }
          return scene
        }),
      }
      // Update total project duration if duration was changed
      if (updates.duration !== undefined) {
        setTimeout(() => get()._updateProjectDuration(), 0)
      }
      return newState
    }),

  updateVoiceoverSpeed: (sceneId, newSpeed) =>
    set(state => {
      state._addToHistoryDebounced()
      return {
        scenes: state.scenes.map(scene => {
          if (scene.id === sceneId) {
            // Initialize originalDuration if not set (backward compatibility)
            const originalDuration = scene.originalDuration ?? scene.duration

            // Calculate new duration based on original duration and new speed
            const newDuration = originalDuration / newSpeed

            // Also need to store original caption timing if not already stored
            const originalCaptions = scene.captions

            return {
              ...scene,
              duration: newDuration,
              originalDuration, // Preserve the original duration
              voiceSettings: {
                ...scene.voiceSettings,
                voiceSpeed: newSpeed,
              },
              // Update media endTime to match new duration
              media: scene.media
                ? {
                    ...scene.media,
                    endTime: newDuration,
                  }
                : scene.media,
              // Update voiceover duration and speed if exists
              voiceover: scene.voiceover
                ? {
                    ...scene.voiceover,
                    audioDuration: newDuration,
                    speed: newSpeed,
                  }
                : scene.voiceover,
              // Keep original captions unchanged - timing adjustment happens during playback
              captions: originalCaptions,
            }
          }
          return scene
        }),
      }
      // Update total project duration after speed change
      get()._updateProjectDuration()
    }),

  // Helper function to update project duration when scenes change
  _updateProjectDuration: () => {
    const state = get()
    if (state.project) {
      const totalDuration = state.scenes.reduce(
        (sum, scene) => sum + (scene.duration || 0),
        0
      )
      set({
        project: {
          ...state.project,
          duration: totalDuration,
        },
      })
    }
  },

  setCurrentScene: sceneId => set({ currentScene: sceneId }),

  setSubtitlePosition: position => {
    set({ subtitlePosition: position })
    get()._addToHistoryDebounced()
  },

  setOrientation: orientation => {
    set({ orientation: orientation })
    get()._addToHistoryDebounced()
  },

  setLoading: loading => set({ isLoading: loading }),

  deleteScene: sceneId =>
    set(state => {
      state._addToHistoryDebounced()

      // Find the scene to delete and its index
      const sceneToDelete = state.scenes.find(scene => scene.id === sceneId)
      const deleteIndex = state.scenes.findIndex(scene => scene.id === sceneId)

      if (!sceneToDelete || deleteIndex === -1) {
        return {}
      }

      const newScenes = state.scenes.filter(scene => scene.id !== sceneId)
      const newCurrentScene = newScenes.length > 0 ? newScenes[0].id : ''

      // Handle podcast flow: recalculate startOffset for subsequent scenes
      const isPodcastOrAudioFlow = !!state.project?.speech
      if (isPodcastOrAudioFlow && sceneToDelete.startOffset !== undefined) {
        const deletedSceneDuration = sceneToDelete.duration || 0

        // Update startOffset for all scenes that come after the deleted scene
        const updatedScenes = newScenes.map((scene, index) => {
          if (index >= deleteIndex) {
            // This scene comes after the deleted scene, reduce its startOffset
            const currentStartOffset = scene.startOffset || 0
            const newStartOffset = Math.max(
              0,
              currentStartOffset - deletedSceneDuration
            )
            return {
              ...scene,
              startOffset: newStartOffset,
            }
          }
          return scene
        })

        return { scenes: updatedScenes, currentScene: newCurrentScene }
      }

      // Regular flow: just remove the scene
      // Update total project duration after deleting scene
      setTimeout(() => get()._updateProjectDuration(), 0)

      return { scenes: newScenes, currentScene: newCurrentScene }
    }),

  duplicateScene: sceneId =>
    set(state => {
      state._addToHistoryDebounced()
      const sceneToDuplicate = state.scenes.find(scene => scene.id === sceneId)
      if (!sceneToDuplicate) return {}

      const duplicatedScene: Scene = {
        ...sceneToDuplicate,
        id: (Date.now() + Math.random()).toString(),
        name: `${sceneToDuplicate.name} (Copy)`,
        text: '',
        originalDuration:
          sceneToDuplicate.originalDuration ?? sceneToDuplicate.duration, // Preserve original duration
        voiceSettings: {
          voiceId: '',
          voiceUrl: '',
          voiceVol: 1,
          voiceName: '',
          voiceSpeed: 1,
        },
        media: {
          id: 'media-new',
          type: 'image',
          url: '',
          position: { x: 0, y: 0 },
          size: { width: 1920, height: 1080 },
          startTime: 0,
          endTime: 5,
          thumbnail: '',
          fit: 'contain',
          transition: 'fade',
          kenBurns: 'zoom-in',
          effectDuration: 1.5,
        },
      }

      const insertIndex = state.scenes.findIndex(scene => scene.id === sceneId)
      const newScenes = [...state.scenes]
      newScenes.splice(insertIndex + 1, 0, duplicatedScene)

      // Update total project duration after duplicating scene
      setTimeout(() => get()._updateProjectDuration(), 0)

      return { scenes: newScenes, currentScene: duplicatedScene.id }
    }),

  // Music actions
  setSelectedMusic: music => {
    set({ selectedMusic: music })
    get()._addToHistoryDebounced()
  },
  setMusicVolume: volume => {
    set({ musicVolume: volume })
    get()._addToHistoryDebounced()
  },
  setMusicEnabled: enabled => {
    set({ musicEnabled: enabled })
    get()._addToHistoryDebounced()
  },

  setCaptionsEnabled: enabled => {
    set({ captionsEnabled: enabled })
    get()._addToHistoryDebounced()
  },

  // History actions
  undo: () =>
    set(state => {
      if (state.historyIndex > 0) {
        const prevHistoryEntry = state.history[state.historyIndex - 1]
        return {
          scenes: JSON.parse(JSON.stringify(prevHistoryEntry.scenes)),
          currentScene: prevHistoryEntry.currentScene,
          subtitlePosition: { ...prevHistoryEntry.subtitlePosition },
          orientation: prevHistoryEntry.orientation,
          selectedMusic: prevHistoryEntry.selectedMusic,
          musicVolume: prevHistoryEntry.musicVolume,
          musicEnabled: prevHistoryEntry.musicEnabled,
          captionsEnabled: prevHistoryEntry.captionsEnabled,
          historyIndex: state.historyIndex - 1,
          isHistoryMutating: true,
        }
      }
      return {}
    }),

  redo: () =>
    set(state => {
      if (state.historyIndex < state.history.length - 1) {
        const nextHistoryEntry = state.history[state.historyIndex + 1]
        return {
          scenes: JSON.parse(JSON.stringify(nextHistoryEntry.scenes)),
          currentScene: nextHistoryEntry.currentScene,
          subtitlePosition: { ...nextHistoryEntry.subtitlePosition },
          orientation: nextHistoryEntry.orientation,
          selectedMusic: nextHistoryEntry.selectedMusic,
          musicVolume: nextHistoryEntry.musicVolume,
          musicEnabled: nextHistoryEntry.musicEnabled,
          captionsEnabled: nextHistoryEntry.captionsEnabled,
          historyIndex: state.historyIndex + 1,
          isHistoryMutating: true,
        }
      }
      return {}
    }),

  canUndo: () => get().historyIndex > 0,
  canRedo: () => get().historyIndex < get().history.length - 1,

  // Utility Actions
  clearVideo: () => {
    set({
      scenes: JSON.parse(JSON.stringify(initialState.scenes)),
      currentScene: initialState.currentScene,
      subtitlePosition: { ...initialState.subtitlePosition },
      orientation: initialState.orientation,
      selectedMusic: null,
      musicVolume: 15,
      musicEnabled: true,
      history: [
        {
          scenes: JSON.parse(JSON.stringify(initialState.scenes)),
          currentScene: initialState.currentScene,
          subtitlePosition: { ...initialState.subtitlePosition },
          orientation: initialState.orientation,
          selectedMusic: null,
          musicVolume: 15,
          musicEnabled: true,
          captionsEnabled: true,
        },
      ],
      historyIndex: 0,
      captionsEnabled: true,
    })
  },

  // Private helpers - not exposed in type but used internally
  _addToHistoryDebounced: () => {
    let timeout: NodeJS.Timeout | null = null
    return () => {
      if (timeout) {
        clearTimeout(timeout)
      }
      set({ isHistoryMutating: false })
      timeout = setTimeout(() => {
        get()._addToHistory()
        timeout = null
      }, 300)
    }
  },

  // Project actions
  setProjectData: (project: VideoProject | ApiVideoProject) => {
    // Check if this is an API response format and transform it
    const transformedProject =
      'scenes' in project &&
      project.scenes.length > 0 &&
      'captions' in project.scenes[0]
        ? transformApiProjectToStore(project as ApiVideoProject)
        : (project as VideoProject)

    // Ensure all scenes have originalDuration (backward compatibility)
    const scenesWithOriginalDuration = transformedProject.scenes.map(scene => ({
      ...scene,
      originalDuration: scene.originalDuration ?? scene.duration,
    }))

    // Create music track from API response if available
    let selectedMusic: MusicTrack | null = null
    if (transformedProject.music?.src) {
      selectedMusic = {
        id: 'api-music',
        title: transformedProject.music.name || 'Background Music',
        genre: 'Background',
        mood: 'Neutral',
        artistName: 'Unknown',
        provider: 'API',
        licenseId: 'api',
        previewUrl: transformedProject.music.src,
        durationMillis: (transformedProject.music.duration || 0) * 1000,
      }
    }

    set({
      project: {
        ...transformedProject,
        scenes: scenesWithOriginalDuration,
        blogImages: transformedProject.blogImages ?? [],
        speech:
          !transformedProject.speech || !transformedProject.speech.src
            ? null
            : transformedProject.speech,
      },
      scenes: scenesWithOriginalDuration,
      orientation: transformedProject.orientation,
      musicEnabled: transformedProject.music?.enabled ?? true,
      musicVolume: transformedProject.music?.volume ?? 15,
      selectedMusic,
      captionsEnabled: transformedProject.captionSettings?.enabled ?? true,
      currentScene: scenesWithOriginalDuration[0]?.id || '',
    })
  },
  updateProjectName: (name: string) =>
    set(state => ({
      project: state.project ? { ...state.project, projectName: name } : null,
    })),

  // Video player actions
  setPlayerRef: (ref: RefObject<PlayerRef | null> | null) => {
    set({ playerRef: ref })
  },
  seekToTime: (timeInSeconds: number) => {
    const state = get()
    if (state.playerRef?.current) {
      const player = state.playerRef.current
      // Convert time to frame and seek
      const fps = 30
      const frame = Math.round(timeInSeconds * fps)
      player.seekTo(frame)
    }
  },
}))

// Initialize subtitle position from localStorage on first load
if (typeof window !== 'undefined') {
  try {
    const savedPosition = localStorage.getItem('subtitlePosition')
    if (savedPosition) {
      const position = JSON.parse(savedPosition)
      useVideoStore.setState({ subtitlePosition: position })
    }
  } catch (error) {
    console.error('Failed to load subtitle position from localStorage:', error)
  }
}
