import { NextRequest, NextResponse } from 'next/server'
import { getAuth } from '@clerk/nextjs/server'
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

function sanitizeFilename(filename: string): string {
  // Replace Unicode dashes with ASCII dash
  let sanitized = filename.replace(/–/g, '-')
  // Normalize to NFKD and remove non-ASCII characters
  sanitized = sanitized.normalize('NFKD').replace(/[^\x00-\x7F]/g, '')
  return sanitized
}

const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.REMOTION_AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.REMOTION_AWS_SECRET_ACCESS_KEY!,
  },
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { userId } = getAuth(request)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { path: pathArr } = await params
    const path = pathArr.join('/')
    const { searchParams } = new URL(request.url)
    const filename = searchParams.get('filename') || 'video.mp4'

    // Validate that the path belongs to the user
    if (!path.startsWith(`renders/${userId}/`)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    const bucketName = process.env.REMOTION_AWS_S3_BUCKET_NAME
    if (!bucketName) {
      return NextResponse.json(
        { error: 'S3 bucket not configured' },
        { status: 500 }
      )
    }

    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: path,
      ResponseContentDisposition: `attachment; filename="${sanitizeFilename(filename)}"`,
    })

    // Generate signed URL that expires in 1 hour
    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 3600, // 1 hour
    })
    console.log('signedUrl', signedUrl)

    return NextResponse.json({ downloadUrl: signedUrl })
  } catch (error) {
    console.error('Error generating AWS download URL:', error)
    return NextResponse.json(
      { error: 'Failed to generate download URL' },
      { status: 500 }
    )
  }
}
