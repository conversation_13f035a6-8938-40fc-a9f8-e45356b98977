import { NextRequest } from 'next/server'
import { db } from '@/lib/db'
import { renderJobs } from '@/db/schema'
import { eq } from 'drizzle-orm'
import { getAuth } from '@clerk/nextjs/server'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = getAuth(request)
    if (!userId) {
      return new Response('Unauthorized', { status: 401 })
    }
    const { id: jobId } = await params
    // Check ownership
    const jobs = await db
      .select()
      .from(renderJobs)
      .where(eq(renderJobs.id, jobId))
    if (!jobs.length || jobs[0].userId !== userId) {
      return new Response('Forbidden: Not your video', { status: 403 })
    }
    await db.delete(renderJobs).where(eq(renderJobs.id, jobId))
    return new Response('Video deleted', { status: 200 })
  } catch (error) {
    console.error('Delete video error:', error)
    return new Response('Delete failed', { status: 500 })
  }
}
