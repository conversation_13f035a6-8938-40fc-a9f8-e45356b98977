import { NextRequest } from 'next/server'
import { getAuth } from '@clerk/nextjs/server'
import { S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { db } from '@/lib/db'
import { renderJobs } from '@/db/schema'
import { eq } from 'drizzle-orm'

const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.REMOTION_AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.REMOTION_AWS_SECRET_ACCESS_KEY!,
  },
})

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { userId } = getAuth(request)
    if (!userId) return new Response('Unauthorized', { status: 401 })

    const { path: pathArr } = await params
    const jobId = request.nextUrl.searchParams.get('jobId')
    if (!jobId) return new Response('Missing jobId', { status: 400 })

    // Check path: renders/userId/...
    if (
      pathArr.length < 3 ||
      pathArr[0] !== 'renders' ||
      pathArr[1] !== userId
    ) {
      return new Response('Forbidden: Not your video', { status: 403 })
    }

    const filePath = pathArr.join('/')
    const bucketName = process.env.REMOTION_AWS_S3_BUCKET_NAME
    if (!bucketName) {
      return new Response('S3 bucket not configured', { status: 500 })
    }

    // Check render job ownership
    const jobs = await db
      .select()
      .from(renderJobs)
      .where(eq(renderJobs.id, jobId))
    if (!jobs.length || jobs[0].userId !== userId) {
      return new Response('Forbidden: Not your job', { status: 403 })
    }

    // Delete file from S3
    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: filePath,
    })
    await s3Client.send(deleteCommand)

    // Delete DB record
    await db.delete(renderJobs).where(eq(renderJobs.id, jobId))

    return new Response('Video and render job deleted', { status: 200 })
  } catch (error) {
    console.error('Delete error:', error)
    return new Response('Delete failed', { status: 500 })
  }
}
