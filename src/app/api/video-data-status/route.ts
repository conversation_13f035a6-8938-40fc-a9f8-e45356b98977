import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  const eventId = req.nextUrl.searchParams.get('eventId')
  if (!eventId) {
    return NextResponse.json(
      { error: 'Missing or invalid eventId' },
      { status: 400 }
    )
  }

  try {
    console.log('🔍 Polling Inngest for eventId:', eventId)

    // Determine the Inngest API URL based on environment
    // For production: use Inngest Cloud API (https://api.inngest.com)
    // For development: use local dev server (http://localhost:8288)
    const isDevelopment = process.env.NODE_ENV === 'development'
    const baseUrl = isDevelopment
      ? 'http://localhost:8288'
      : 'https://api.inngest.com'

    const apiUrl = `${baseUrl}/v1/events/${eventId}/runs`
    console.log('🔍 Making request to:', apiUrl)

    // Use appropriate authorization based on environment
    const authToken = isDevelopment ? 'dev' : process.env.INNGEST_SIGNING_KEY

    if (!isDevelopment && !process.env.INNGEST_SIGNING_KEY) {
      console.error('❌ INNGEST_SIGNING_KEY not set in production')
      return NextResponse.json(
        { error: 'Inngest configuration error - signing key not set' },
        { status: 500 }
      )
    }

    const inngestRes = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    })

    console.log('🔍 Inngest API response status:', inngestRes.status)

    if (!inngestRes.ok) {
      const errorText = await inngestRes.text()
      console.error('❌ Inngest API error:', inngestRes.status, errorText)

      // If it's a 404, the run might not exist yet - this is expected initially
      if (inngestRes.status === 404) {
        return NextResponse.json(
          {
            error: 'No runs found for this event',
            details:
              'The Inngest function run may not have started yet. This is normal - the event is queued and will be processed shortly.',
            retryable: true,
          },
          { status: 404 }
        )
      }

      return NextResponse.json(
        {
          error: 'Failed to fetch from Inngest',
          details: errorText,
          status: inngestRes.status,
        },
        { status: inngestRes.status }
      )
    }

    const json = await inngestRes.json()
    console.log('✅ Inngest API response:', JSON.stringify(json, null, 2))

    if (!json.data || !Array.isArray(json.data)) {
      console.error('❌ Invalid response format from Inngest:', json)
      return NextResponse.json(
        { error: 'Invalid response format from Inngest' },
        { status: 500 }
      )
    }

    if (json.data.length === 0) {
      console.log('⚠️ No runs found for eventId:', eventId)
      return NextResponse.json(
        {
          error: 'No runs found for this event',
          details:
            'The Inngest function run may not have started yet. This is normal - the event is queued and will be processed shortly.',
          retryable: true,
        },
        { status: 404 }
      )
    }

    console.log('✅ Returning runs data:', json.data.length, 'runs found')
    return NextResponse.json(json.data)
  } catch (error) {
    console.error('❌ Error in video-data-status route:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
