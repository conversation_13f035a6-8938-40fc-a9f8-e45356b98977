import { NextRequest } from 'next/server'
import { Storage } from '@google-cloud/storage'
import { getAuth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'
import { renderJobs } from '@/db/schema'
import { eq } from 'drizzle-orm'

const storage = new Storage({
  projectId: process.env.REMOTION_GCP_PROJECT_ID,
  credentials: {
    type: 'service_account',
    client_email: process.env.REMOTION_GCP_CLIENT_EMAIL,
    private_key: process.env.REMOTION_GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    project_id: process.env.REMOTION_GCP_PROJECT_ID,
  },
})

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { userId } = getAuth(request)
    if (!userId) return new Response('Unauthorized', { status: 401 })

    const { path: pathArr } = await params
    const jobId = request.nextUrl.searchParams.get('jobId')
    if (!jobId) return new Response('Missing jobId', { status: 400 })

    // Check path: renders/userId/...
    if (
      pathArr.length < 3 ||
      pathArr[0] !== 'renders' ||
      pathArr[1] !== userId
    ) {
      return new Response('Forbidden: Not your video', { status: 403 })
    }
    const filePath = pathArr.join('/')
    const bucket = storage.bucket('remotioncloudrun-iexx606ijk')
    const file = bucket.file(filePath)

    // Check file exists
    const [exists] = await file.exists()
    if (!exists) return new Response('Video not found', { status: 404 })

    // Check render job ownership
    const jobs = await db
      .select()
      .from(renderJobs)
      .where(eq(renderJobs.id, jobId))
    if (!jobs.length || jobs[0].userId !== userId) {
      return new Response('Forbidden: Not your job', { status: 403 })
    }

    // Delete file
    await file.delete()
    // Delete DB record
    await db.delete(renderJobs).where(eq(renderJobs.id, jobId))

    return new Response('Video and render job deleted', { status: 200 })
  } catch (error) {
    console.error('Delete error:', error)
    return new Response('Delete failed', { status: 500 })
  }
}
