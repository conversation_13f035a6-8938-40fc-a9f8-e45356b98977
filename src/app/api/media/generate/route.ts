import { NextRequest, NextResponse } from 'next/server'
import { createErrorResponse } from '@/lib/api-cache'

const GETIMG_API_KEY = process.env.GETIMG_API_KEY

export async function POST(request: NextRequest) {
  if (!GETIMG_API_KEY) {
    return createErrorResponse('GetImg.ai API key not configured', 500)
  }

  try {
    const body = await request.json()
    const { prompt, model = 'flux-1-schnell', orientation = 'landscape' } = body

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 })
    }

    // Map model to GetImg.ai endpoint
    const modelEndpoints: Record<string, string> = {
      'stable-diffusion-xl':
        'https://api.getimg.ai/v1/stable-diffusion-xl/text-to-image',
      'stable-diffusion-v1-6':
        'https://api.getimg.ai/v1/stable-diffusion/text-to-image',
      'flux-1-schnell': 'https://api.getimg.ai/v1/flux-schnell/text-to-image',
    }

    const endpoint = modelEndpoints[model] || modelEndpoints['flux-1-schnell']

    // Handle different model parameter requirements
    // All models use width/height according to GetImg.ai documentation
    let dimensionMap: Record<string, { width: number; height: number }>

    if (model === 'flux-1-schnell') {
      // FLUX models use width/height with standard 16:9 ratios
      dimensionMap = {
        landscape: { width: 1024, height: 576 }, // 16:9 landscape
        portrait: { width: 576, height: 1024 }, // 9:16 portrait
        square: { width: 1024, height: 1024 }, // 1:1 square
      }
    } else {
      // Stable Diffusion models use standard dimensions
      dimensionMap = {
        landscape: { width: 1024, height: 576 },
        portrait: { width: 576, height: 1024 },
        square: { width: 1024, height: 1024 },
      }
    }

    const dimensions = dimensionMap[orientation] || dimensionMap.landscape

    // Build request body based on model
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let requestBody: Record<string, any>

    if (model === 'flux-1-schnell') {
      // FLUX.1 schnell - only supports basic parameters
      requestBody = {
        prompt: prompt,
        output_format: 'jpeg',
        width: dimensions.width,
        height: dimensions.height,
      }
    } else {
      // Stable Diffusion models - support steps parameter
      requestBody = {
        prompt: prompt,
        width: dimensions.width,
        height: dimensions.height,
        output_format: 'jpeg',
        steps: 20, // Standard for Stable Diffusion models
      }
    }

    console.log(
      `Model: ${model} Orientation: ${orientation} Request body:`,
      requestBody
    )

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${GETIMG_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`GetImg.ai API error: ${errorText}`)
    }

    const data = await response.json()
    console.log(data.cost)

    // Return the base64 image data with dimensions
    return NextResponse.json({
      imageUrl: `data:image/jpeg;base64,${data.image}`,
      width: dimensions.width,
      height: dimensions.height,
    })
  } catch (error) {
    console.error('Image generation API error:', error)
    return createErrorResponse('Failed to generate image', 500)
  }
}
