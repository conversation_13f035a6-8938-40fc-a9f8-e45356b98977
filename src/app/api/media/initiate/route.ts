import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'
import { db } from '@/lib/db'
import { mediaAssets } from '@/db/schema'
import { eq } from 'drizzle-orm'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Define allowed media types
const ALLOWED_MEDIA_TYPES = {
  // Images
  'image/jpeg': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'JPEG Image',
  },
  'image/jpg': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'JPG Image',
  },
  'image/png': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'PNG Image',
  },
  'image/webp': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'WebP Image',
  },
  'image/gif': {
    bucket: 'assets',
    maxSize: 50 * 1024 * 1024,
    label: 'GIF Image',
  },

  // Videos
  'video/mp4': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'MP4 Video',
  },
  'video/webm': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'WebM Video',
  },
  'video/quicktime': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'QuickTime Video',
  },
  'video/x-msvideo': {
    bucket: 'assets',
    maxSize: 500 * 1024 * 1024,
    label: 'AVI Video',
  },

  // Audio
  'audio/mpeg': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'MP3 Audio',
  },
  'audio/mp3': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'MP3 Audio',
  },
  'audio/wav': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WAV Audio',
  },
  'audio/wave': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WAV Audio',
  },
  'audio/x-wav': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WAV Audio',
  },
  'audio/aac': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'AAC Audio',
  },
  'audio/ogg': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'OGG Audio',
  },
  'audio/webm': {
    bucket: 'assets',
    maxSize: 100 * 1024 * 1024,
    label: 'WebM Audio',
  },
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const thumbnailFile = formData.get('thumbnail') as File | null
    const metadataStr = formData.get('metadata') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!metadataStr) {
      return NextResponse.json(
        { error: 'No metadata provided' },
        { status: 400 }
      )
    }

    let metadata
    try {
      metadata = JSON.parse(metadataStr)
    } catch {
      return NextResponse.json(
        { error: 'Invalid metadata format' },
        { status: 400 }
      )
    }

    // Check if file type is allowed
    const fileConfig =
      ALLOWED_MEDIA_TYPES[file.type as keyof typeof ALLOWED_MEDIA_TYPES]
    if (!fileConfig) {
      return NextResponse.json(
        {
          error: `Invalid file type. Allowed types: ${Object.keys(
            ALLOWED_MEDIA_TYPES
          )
            .map(
              type =>
                ALLOWED_MEDIA_TYPES[type as keyof typeof ALLOWED_MEDIA_TYPES]
                  .label
            )
            .join(', ')}`,
        },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > fileConfig.maxSize) {
      const maxSizeMB = fileConfig.maxSize / (1024 * 1024)
      return NextResponse.json(
        { error: `File too large. Maximum size is ${maxSizeMB}MB.` },
        { status: 400 }
      )
    }

    // Determine file type and folder
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')
    const isAudio = file.type.startsWith('audio/')

    let folder: string
    if (isImage) {
      folder = 'images'
    } else if (isVideo) {
      folder = 'videos'
    } else if (isAudio) {
      folder = 'musics'
    } else {
      folder = 'others'
    }

    // Create file paths
    const timestamp = Date.now()
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
    const filePath = `${userId}/${folder}/${timestamp}-${sanitizedFileName}`
    const thumbnailPath = thumbnailFile
      ? `${userId}/thumbnails/${timestamp}-thumb-${sanitizedFileName.replace(/\.[^/.]+$/, '.jpg')}`
      : null

    // Create initial database record with "processing" status
    const mediaRecord = {
      userId,
      fileName: `${timestamp}-${sanitizedFileName}`,
      originalName: file.name,
      mimeType: file.type,
      fileSize: file.size,
      originalUrl: '', // Will be updated after upload
      thumbnailUrl: null,
      width: metadata.width || null,
      height: metadata.height || null,
      duration: metadata.duration ? metadata.duration.toString() : null,
      quality: metadata.quality || null,
      metadata: {
        aspectRatio: metadata.aspectRatio || 1,
        orientation: metadata.orientation || 'square',
        dominantColors: metadata.dominantColors || [],
        tags: [],
        description: undefined,
        alt: undefined,
        status: 'processing', // Add processing status
        uploadProgress: 0,
      },
    }

    const [insertedRecord] = await db
      .insert(mediaAssets)
      .values(mediaRecord)
      .returning()

    // Start background upload process (don't await)
    processUploadInBackground(
      insertedRecord.id,
      file,
      thumbnailFile,
      filePath,
      thumbnailPath,
      fileConfig.bucket,
      metadata
    )

    // Return immediately with upload ID
    return NextResponse.json({
      id: insertedRecord.id,
      status: 'processing',
      message: 'Upload initiated. Use the status API to check progress.',
    })
  } catch (error) {
    console.error('Upload initiation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Background processing function
async function processUploadInBackground(
  recordId: string,
  file: File,
  thumbnailFile: File | null,
  filePath: string,
  thumbnailPath: string | null,
  bucket: string,
  clientMetadata: {
    aspectRatio?: number
    orientation?: 'landscape' | 'portrait' | 'square'
    dominantColors?: string[]
  }
) {
  try {
    // Update status to uploading
    await updateUploadStatus(
      recordId,
      'uploading',
      25,
      'Uploading original file...',
      clientMetadata
    )

    // Upload original file
    const { error: uploadError } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        upsert: false,
        contentType: file.type,
      })

    if (uploadError) {
      await updateUploadStatus(
        recordId,
        'error',
        0,
        `Upload failed: ${uploadError.message}`,
        clientMetadata
      )
      return
    }

    // Get original URL
    const { data: originalUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    await updateUploadStatus(
      recordId,
      'uploading',
      60,
      'Uploading thumbnail...',
      clientMetadata
    )

    // Upload thumbnail if provided
    let thumbnailUrl: string | undefined
    if (thumbnailFile && thumbnailPath) {
      const { error: thumbError } = await supabase.storage
        .from(bucket)
        .upload(thumbnailPath, thumbnailFile, {
          upsert: false,
          contentType: 'image/jpeg',
        })

      if (!thumbError) {
        const { data: thumbUrlData } = supabase.storage
          .from(bucket)
          .getPublicUrl(thumbnailPath)
        thumbnailUrl = thumbUrlData.publicUrl
      }
    }

    await updateUploadStatus(
      recordId,
      'finalizing',
      90,
      'Finalizing upload...',
      clientMetadata
    )

    // Update record with final URLs and complete status
    await db
      .update(mediaAssets)
      .set({
        originalUrl: originalUrlData.publicUrl,
        thumbnailUrl: thumbnailUrl || null,
        metadata: {
          aspectRatio: clientMetadata.aspectRatio || 1,
          orientation: clientMetadata.orientation || 'square',
          dominantColors: clientMetadata.dominantColors || [],
          tags: [],
          description: undefined,
          alt: undefined,
        },
        updatedAt: new Date(),
      })
      .where(eq(mediaAssets.id, recordId))
  } catch (error) {
    console.error('Background upload error:', error)
    await updateUploadStatus(
      recordId,
      'error',
      0,
      `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      clientMetadata
    )
  }
}

async function updateUploadStatus(
  recordId: string,
  _status: string,
  _progress: number,
  _message: string,
  clientMetadata?: {
    aspectRatio?: number
    orientation?: 'landscape' | 'portrait' | 'square'
    dominantColors?: string[]
  }
) {
  try {
    await db
      .update(mediaAssets)
      .set({
        metadata: {
          aspectRatio: clientMetadata?.aspectRatio || 1,
          orientation: clientMetadata?.orientation || 'square',
          dominantColors: clientMetadata?.dominantColors || [],
          tags: [],
          description: undefined,
          alt: undefined,
        },
        updatedAt: new Date(),
      })
      .where(eq(mediaAssets.id, recordId))
  } catch (error) {
    console.error('Failed to update upload status:', error)
  }
}
