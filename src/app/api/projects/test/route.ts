import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Test the projects API
    const testResponse = await fetch(
      `${request.nextUrl.origin}/api/projects?page=1&limit=8`,
      {
        headers: {
          Authorization: request.headers.get('Authorization') || '',
          Cookie: request.headers.get('Cookie') || '',
        },
      }
    )

    const testData = await testResponse.json()

    return NextResponse.json({
      message: 'Projects API test successful',
      userId,
      testData,
      status: testResponse.status,
    })
  } catch (error) {
    console.error('Test API error:', error)
    return NextResponse.json(
      { error: 'Test failed', details: error },
      { status: 500 }
    )
  }
}
