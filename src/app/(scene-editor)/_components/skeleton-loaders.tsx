'use client'

import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'

// Header skeleton
export function VideoEditorHeaderSkeleton() {
  return (
    <div className='flex items-center justify-between px-4 py-3 border-b bg-card'>
      {/* Left side - Logo and project name */}
      <div className='flex items-center gap-3'>
        <Skeleton className='w-8 h-8 rounded-lg' />
        <div className='flex flex-col gap-1'>
          <Skeleton className='w-32 h-4' />
          <Skeleton className='w-24 h-3' />
        </div>
      </div>

      {/* Center - History controls */}
      <div className='flex items-center gap-2'>
        <Skeleton className='w-8 h-8 rounded' />
        <Skeleton className='w-8 h-8 rounded' />
      </div>

      {/* Right side - Export and user */}
      <div className='flex items-center gap-3'>
        <Skeleton className='w-20 h-8 rounded-md' />
        <Skeleton className='w-24 h-8 rounded-md' />
        <Skeleton className='w-8 h-8 rounded-full' />
      </div>
    </div>
  )
}

// Scene card skeleton for the sidebar
export function SceneCardSkeleton({ index }: { index: number }) {
  return (
    <div className='rounded-lg border bg-card shadow-sm border-border overflow-hidden'>
      <AccordionItem value={`skeleton-${index}`} className='border-0'>
        <AccordionTrigger className='px-0 py-0 border-b bg-muted/40 hover:no-underline [&>svg]:hidden'>
          <div className='w-full'>
            {/* Header Row */}
            <div className='flex items-center justify-between px-4 py-3'>
              <div className='flex items-center gap-3'>
                <Skeleton className='w-6 h-6 rounded' />
                <div className='flex flex-col gap-1'>
                  <Skeleton className='w-16 h-4' />
                  <Skeleton className='w-12 h-3' />
                </div>
              </div>

              <div className='flex items-center gap-2'>
                <Skeleton className='w-20 h-6 rounded-full' />
                <Skeleton className='w-8 h-8 rounded' />
                <Skeleton className='w-8 h-8 rounded' />
              </div>
            </div>

            {/* Preview Row */}
            <div className='flex items-center gap-3 px-4 pb-3'>
              <Skeleton className='w-16 h-12 rounded' />
              <div className='flex-1'>
                <Skeleton className='w-full h-4 mb-1' />
                <Skeleton className='w-3/4 h-3' />
              </div>
            </div>
          </div>
        </AccordionTrigger>
      </AccordionItem>
    </div>
  )
}

// Sidebar skeleton with tabs
export function ScenesSidebarSkeleton() {
  return (
    <div className='h-full flex flex-col bg-background'>
      <Tabs defaultValue='scenes' className='flex flex-col h-full'>
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='scenes'>
            <Skeleton className='w-12 h-4' />
          </TabsTrigger>
          <TabsTrigger value='settings'>
            <Skeleton className='w-16 h-4' />
          </TabsTrigger>
        </TabsList>

        <TabsContent value='scenes' className='flex-1 mt-4 overflow-y-auto'>
          <div className='space-y-4 px-4'>
            {/* Add Scene Button */}
            <Skeleton className='w-full h-10 rounded-md' />

            {/* Scene Cards */}
            <Accordion type='single' collapsible className='w-full space-y-4'>
              {Array.from({ length: 3 }).map((_, index) => (
                <SceneCardSkeleton key={index} index={index} />
              ))}
            </Accordion>
          </div>
        </TabsContent>

        <TabsContent value='settings' className='flex-1 mt-4 overflow-y-auto'>
          <div className='space-y-6 px-4'>
            {/* Music Settings */}
            <div className='space-y-3'>
              <Skeleton className='w-20 h-5' />
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <Skeleton className='w-4 h-4 rounded' />
                  <Skeleton className='w-24 h-4' />
                </div>
                <Skeleton className='w-full h-8 rounded' />
                <Skeleton className='w-full h-6' />
              </div>
            </div>

            {/* Caption Settings */}
            <div className='space-y-3'>
              <Skeleton className='w-24 h-5' />
              <div className='space-y-2'>
                <Skeleton className='w-full h-8 rounded' />
                <Skeleton className='w-full h-8 rounded' />
                <Skeleton className='w-full h-8 rounded' />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Video preview area skeleton
export function VideoPreviewSkeleton() {
  return (
    <div className='h-full flex flex-col bg-background'>
      {/* Video Info Bar */}
      <div className='flex items-center justify-between px-4 py-2 border-b bg-card flex-shrink-0'>
        <div className='flex items-center gap-4'>
          <Skeleton className='w-28 h-5' />
        </div>

        <div className='flex items-center gap-2'>
          <Skeleton className='w-32 h-8 rounded' />
          <Skeleton className='w-20 h-6 rounded-full' />
        </div>
      </div>

      {/* Video Preview Display */}
      <div className='flex-1 min-h-0 flex items-center justify-center p-6 bg-muted/40'>
        <div className='relative'>
          <Skeleton className='w-80 h-52 sm:w-96 sm:h-64 rounded-lg' />
          {/* Loading indicator overlay */}
          <div className='absolute inset-0 flex items-center justify-center'>
            <div className='flex flex-col items-center gap-3 text-muted-foreground'>
              <div className='w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin' />
              <span className='text-sm animate-pulse'>Loading project...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Full layout skeleton
export function SceneEditorSkeleton() {
  return (
    <div className='h-screen bg-background flex flex-col overflow-hidden'>
      {/* Header */}
      <VideoEditorHeaderSkeleton />

      {/* Main Content Area */}
      <div className='flex-1 min-h-0 flex flex-row'>
        {/* Left Sidebar - Desktop */}
        <div className='hidden md:flex flex-col w-2/5 min-w-0 max-w-[40vw] h-full overflow-y-auto border-r'>
          <ScenesSidebarSkeleton />
        </div>

        {/* Right Video Area - Desktop */}
        <div className='hidden md:flex flex-col w-3/5 min-w-0 max-w-[60vw] h-full'>
          <VideoPreviewSkeleton />
        </div>

        {/* Mobile View */}
        <div className='flex md:hidden flex-col w-full h-full min-h-0 overflow-y-auto'>
          <ScenesSidebarSkeleton />
        </div>
      </div>

      {/* Mobile Bottom Nav */}
      <div className='md:hidden fixed bottom-0 left-0 w-full bg-card border-t flex z-40'>
        <div className='flex-1 flex items-center justify-center gap-2 py-3'>
          <Skeleton className='w-4 h-4' />
          <Skeleton className='w-20 h-4' />
        </div>
        <div className='flex-1 flex items-center justify-center gap-2 py-3'>
          <Skeleton className='w-4 h-4' />
          <Skeleton className='w-24 h-4' />
        </div>
      </div>
    </div>
  )
}
