'use client'

import React, { useState, useRef } from 'react'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Mic, RotateCw, Play, Pause } from 'lucide-react'
import { useGenerateSpeechMutation } from '@/hooks/useGenerateSpeechMutation'
import { Scene } from '@/types/video'
import { toast } from '@/lib/toast'
import { useUser } from '@clerk/nextjs'
// import { clearCaptions } from '../remotion/subtitles/subtitle-system'

interface VoiceoverSectionProps {
  scene: Scene
  sceneIndex: number
  onOpenVoicePicker: () => void
  onUpdateScene: (id: string, data: Partial<Scene>) => void
}

export function VoiceoverSection({
  scene,
  sceneIndex,
  onOpenVoicePicker,
  onUpdateScene,
}: VoiceoverSectionProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const { user } = useUser()

  const generateSpeech = useGenerateSpeechMutation()

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdateScene(scene.id, {
      text: e.target.value,
    })
  }

  const handleGenerateVoiceover = async () => {
    if (!scene.voiceSettings.voiceId || !scene.text) {
      toast.warning('Please select a voice and enter text first')
      return
    }

    toast.promise(
      generateSpeech.mutateAsync({
        text: scene.text,
        voice_id: scene.voiceSettings.voiceId,
        userId: user?.id, // Pass userId
      }),
      {
        loading: 'Generating voiceover...',
        success: result => {
          // Create audio element to get duration
          const audio = new Audio(result.audioUrl)
          audio.onloadedmetadata = () => {
            // Generate captions using the centralized function
            import('@/lib/remotion/utils/captionUtils').then(
              ({ generateCaptionsFromElevenLabs, generateBasicCaptions }) => {
                const captions = result.alignment
                  ? generateCaptionsFromElevenLabs(
                      scene.text,
                      result.alignment,
                      audio.duration
                    )
                  : generateBasicCaptions(scene.text, audio.duration)

                onUpdateScene(scene.id, {
                  voiceSettings: {
                    ...scene.voiceSettings,
                    voiceUrl: result.audioUrl, // Use voiceUrl to match existing schema
                    alignment: result.alignment, // Save alignment data for future use
                  },
                  // Update voiceover field to ensure new audio is used (has higher priority in Remotion)
                  voiceover: {
                    audioUrl: result.audioUrl,
                    audioDuration: audio.duration,
                    volume: scene.voiceSettings.voiceVol || 100,
                    speed: scene.voiceSettings.voiceSpeed || 1,
                  },
                  duration: audio.duration,
                  captions: captions, // Save generated captions
                })

                // Force Remotion player to refresh by seeking to current time
                setTimeout(async () => {
                  const { useVideoStore } = await import('@/store/video-store')
                  const currentPlayer =
                    useVideoStore.getState().playerRef?.current
                  if (currentPlayer) {
                    const currentFrame = currentPlayer.getCurrentFrame() || 0
                    currentPlayer.seekTo(currentFrame)
                  }
                }, 100)
              }
            )
          }

          audio.onerror = () => {
            // Even on audio error, try to generate captions if we have alignment
            import('@/lib/remotion/utils/captionUtils').then(
              ({ generateCaptionsFromElevenLabs, generateBasicCaptions }) => {
                const captions = result.alignment
                  ? generateCaptionsFromElevenLabs(
                      scene.text,
                      result.alignment,
                      5
                    ) // fallback duration
                  : generateBasicCaptions(scene.text, 5)

                onUpdateScene(scene.id, {
                  voiceSettings: {
                    ...scene.voiceSettings,
                    voiceUrl: result.audioUrl, // Use voiceUrl to match existing schema
                    alignment: result.alignment,
                  },
                  // Update voiceover field to ensure new audio is used (has higher priority in Remotion)
                  voiceover: {
                    audioUrl: result.audioUrl,
                    audioDuration: 5, // fallback duration
                    volume: scene.voiceSettings.voiceVol || 100,
                    speed: scene.voiceSettings.voiceSpeed || 1,
                  },
                  captions: captions,
                })

                // Force Remotion player to refresh by seeking to current time
                setTimeout(async () => {
                  const { useVideoStore } = await import('@/store/video-store')
                  const currentPlayer =
                    useVideoStore.getState().playerRef?.current
                  if (currentPlayer) {
                    const currentFrame = currentPlayer.getCurrentFrame() || 0
                    currentPlayer.seekTo(currentFrame)
                  }
                }, 100)
              }
            )
          }

          return 'Voiceover generated successfully!'
        },
        error: error => {
          console.error('Failed to generate voiceover:', error)
          return 'Failed to generate voiceover'
        },
      }
    )
  }

  const handlePlayPause = () => {
    if (!scene.voiceSettings.voiceUrl) return

    if (isPlaying) {
      // Pause audio
      if (audioRef.current) {
        audioRef.current.pause()
        setIsPlaying(false)
      }
    } else {
      // Play audio
      if (audioRef.current) {
        audioRef.current.src = scene.voiceSettings.voiceUrl
        audioRef.current.play()
        setIsPlaying(true)

        // Set up event listeners
        audioRef.current.onended = () => setIsPlaying(false)
        audioRef.current.onpause = () => setIsPlaying(false)
      } else {
        // Create new audio element
        const audio = new Audio(scene.voiceSettings.voiceUrl)
        audioRef.current = audio
        audio.play()
        setIsPlaying(true)

        // Set up event listeners
        audio.onended = () => setIsPlaying(false)
        audio.onpause = () => setIsPlaying(false)
      }
    }
  }

  return (
    <div className='space-y-4'>
      {/* Hidden audio element for playback */}
      <audio ref={audioRef} style={{ display: 'none' }} />

      <div className='flex items-center gap-2 mb-2'>
        <Label className='text-sm font-medium'>
          Scene {sceneIndex + 1} • Voiceover
        </Label>
        <Button
          variant='outline'
          size='sm'
          className='h-6 px-2 text-xs gap-1'
          onClick={onOpenVoicePicker}
        >
          <Mic className='w-3 h-3' />
          <span className='w-2 h-2 bg-primary rounded-full'></span>
          {scene.voiceSettings.voiceName || 'Select Voice'}
        </Button>

        {/* Generate Button */}
        {scene.voiceSettings.voiceId && (
          <Button
            variant='outline'
            size='sm'
            className='h-6 w-6 p-0'
            title='Generate voiceover'
            onClick={handleGenerateVoiceover}
            disabled={generateSpeech.isPending}
          >
            <RotateCw
              className={`w-3 h-3 ${generateSpeech.isPending ? 'animate-spin' : ''}`}
            />
          </Button>
        )}

        {/* Play/Pause Button */}
        {scene.voiceSettings.voiceUrl && (
          <Button
            variant='outline'
            size='sm'
            className='h-6 w-6 p-0'
            title={isPlaying ? 'Pause voiceover' : 'Play voiceover'}
            onClick={handlePlayPause}
          >
            {isPlaying ? (
              <Pause className='w-3 h-3' />
            ) : (
              <Play className='w-3 h-3' />
            )}
          </Button>
        )}

        {/* Duration Badge */}
        {typeof scene.duration === 'number' && (
          <Badge variant='outline' className='h-5 px-1.5 text-[10px]'>
            {formatDuration(scene.duration)}
          </Badge>
        )}
      </div>

      <Textarea
        value={scene.text || ''}
        onChange={handleTextChange}
        placeholder='Enter the voiceover text for this scene...'
        className='min-h-[120px]'
      />
    </div>
  )
}
