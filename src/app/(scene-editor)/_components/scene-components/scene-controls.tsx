'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Droplet, Crop } from 'lucide-react'
import {
  useDebounceSceneUpdate,
  useDebounceVoiceoverSpeed,
} from '@/hooks/use-debounced-scene-updates'
import type { Scene } from '@/types/video'

interface SceneControlsProps {
  scene: Scene
  onUpdateScene: (updates: Partial<Scene>) => void
  onUpdateVoiceoverSpeed: (speed: number) => void
  hideAudioControls?: boolean
}

export function SceneControls({
  scene,
  onUpdateScene,
  onUpdateVoiceoverSpeed,
  hideAudioControls = false,
}: SceneControlsProps) {
  // Local state for responsive UI
  const [localVoiceVol, setLocalVoiceVol] = useState(
    scene.voiceSettings.voiceVol
  )
  const [localVoiceSpeed, setLocalVoiceSpeed] = useState(
    scene.voiceSettings.voiceSpeed
  )

  // Debounced update hooks
  const debouncedUpdateScene = useDebounceSceneUpdate(scene.id, 100) // 100ms for sliders
  const debouncedUpdateSpeed = useDebounceVoiceoverSpeed(scene.id, 100)

  // Sync local state with scene changes
  useEffect(() => {
    setLocalVoiceVol(scene.voiceSettings.voiceVol)
  }, [scene.voiceSettings.voiceVol])

  useEffect(() => {
    setLocalVoiceSpeed(scene.voiceSettings.voiceSpeed)
  }, [scene.voiceSettings.voiceSpeed])

  return (
    <div className='mt-4 space-y-4'>
      {/* Audio Controls Row (conditionally rendered) */}
      {!hideAudioControls && (
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6'>
          {/* Audio Volume */}
          <div>
            <Label className='text-xs font-medium text-muted-foreground mb-2 block'>
              Audio Volume
            </Label>
            <div className='space-y-2'>
              <Slider
                value={[localVoiceVol]}
                onValueChange={([value]) => {
                  // Update local state immediately for responsive UI
                  setLocalVoiceVol(value)
                  // Debounce the store update
                  debouncedUpdateScene({
                    voiceSettings: {
                      ...scene.voiceSettings,
                      voiceVol: value,
                    },
                  })
                }}
                max={100}
                min={0}
                step={1}
                className='w-full'
              />
              <div className='flex justify-between items-center'>
                <span className='text-xs text-muted-foreground'>0%</span>
                <span className='text-sm font-medium'>{localVoiceVol}%</span>
                <span className='text-xs text-muted-foreground'>100%</span>
              </div>
            </div>
          </div>

          {/* Audio Speed */}
          <div>
            <Label className='text-xs font-medium text-muted-foreground mb-2 block'>
              Audio Speed
            </Label>
            <div className='space-y-2'>
              <Slider
                value={[localVoiceSpeed]}
                onValueChange={([value]) => {
                  // Update local state immediately for responsive UI
                  setLocalVoiceSpeed(value)
                  // Debounce the store update
                  debouncedUpdateSpeed(value)
                }}
                max={2}
                min={0.5}
                step={0.1}
                className='w-full'
              />
              <div className='flex justify-between items-center'>
                <span className='text-xs text-muted-foreground'>0.5x</span>
                <span className='text-sm font-medium'>{localVoiceSpeed}x</span>
                <span className='text-xs text-muted-foreground'>2x</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Background Fit */}
      <div>
        <Label className='text-xs font-medium text-muted-foreground mb-3 block'>
          Background Fit
        </Label>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-4'>
          <button
            className={`h-10 rounded-md border flex items-center justify-center gap-2 transition-all text-sm font-medium ${
              scene.media?.fit === 'blur' || !scene.media?.fit
                ? 'bg-primary text-primary-foreground border-primary'
                : 'border-input bg-background hover:bg-accent hover:text-accent-foreground'
            }`}
            onClick={() => {
              if (scene.media) {
                onUpdateScene({
                  media: {
                    ...scene.media,
                    fit: 'blur',
                  },
                })
              }
            }}
            type='button'
          >
            <div className='w-4 h-4 bg-current/20 rounded flex items-center justify-center'>
              <Droplet className='h-3 w-3' />
            </div>
            <span>Blur</span>
          </button>
          <button
            className={`h-10 rounded-md border flex items-center justify-center gap-2 transition-all text-sm font-medium ${
              scene.media?.fit === 'crop'
                ? 'bg-primary text-primary-foreground border-primary'
                : 'border-input bg-background hover:bg-accent hover:text-accent-foreground'
            }`}
            onClick={() => {
              if (scene.media) {
                onUpdateScene({
                  media: {
                    ...scene.media,
                    fit: 'crop',
                  },
                })
              }
            }}
            type='button'
          >
            <div className='w-4 h-4 bg-current/20 rounded flex items-center justify-center'>
              <Crop className='h-3 w-3' />
            </div>
            <span>Crop</span>
          </button>
        </div>
      </div>
    </div>
  )
}
