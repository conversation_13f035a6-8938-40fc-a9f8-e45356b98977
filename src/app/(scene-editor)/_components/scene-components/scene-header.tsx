'use client'

import { Badge } from '@/components/ui/badge'
import {
  <PERSON><PERSON>,
  RotateCw,
  Play,
  Pause,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react'
import type { Scene } from '@/types/video'

interface SceneHeaderProps {
  scene: Scene
  sceneIndex: number
  isGenerating: boolean
  isPlaying: boolean
  onVoicePickerClick: () => void
  onGenerateVoiceover: () => void
  onPlayPause: () => void
  formatDuration: (duration: number) => string
  hideVoicePicker?: boolean
  isPodcastOrAudioFlow?: boolean
}

function getShortVoiceName(name?: string) {
  if (!name) return ''
  // Responsive truncation based on screen size
  // Most aggressive on smaller screens (50/50 split), more lenient on larger screens
  return name
}

export function SceneHeader({
  scene,
  sceneIndex,
  isGenerating,
  isPlaying,
  onVoicePickerClick,
  onGenerateVoiceover,
  onPlayPause,
  formatDuration,
  hideVoicePicker = false,
  isPodcastOrAudioFlow = false,
}: SceneHeaderProps) {
  const duration = scene.duration
  const hasVoiceover = !!scene.voiceSettings?.voiceUrl
  const hasIssues = !hasVoiceover

  return (
    <div className='flex items-center justify-between px-4 py-2 md:py-3'>
      <div className='flex items-center gap-2 md:gap-3'>
        <span className='font-medium text-sm'>Scene {sceneIndex + 1}</span>

        {/* Voice Selection Button (conditionally rendered) */}
        {!hideVoicePicker && (
          <div
            role='button'
            tabIndex={0}
            aria-disabled={!!(scene.text && scene.text.length > 200)}
            className={
              'inline-flex items-center justify-center gap-1 md:gap-2 hover:border-primary transition-all duration-200 whitespace-nowrap rounded-md text-xs font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-7 md:h-8 px-2 md:px-3' +
              (scene.text && scene.text.length > 200
                ? ' opacity-50 cursor-not-allowed'
                : '')
            }
            onClick={e => {
              e.stopPropagation()
              if (!(scene.text && scene.text.length > 200)) {
                onVoicePickerClick()
              }
            }}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                if (!(scene.text && scene.text.length > 200)) {
                  onVoicePickerClick()
                }
              }
            }}
          >
            <Mic className='w-4 h-4 text-primary flex-shrink-0' />
            <span className='truncate max-w-[60px] md:max-w-[80px] lg:max-w-[100px] xl:max-w-[120px] 2xl:max-w-[140px]'>
              {getShortVoiceName(scene.voiceSettings.voiceName) ||
                'Select Voice'}
            </span>
          </div>
        )}

        {/* Generate Button */}
        {!hideVoicePicker && scene.voiceSettings.voiceId && scene.text && (
          <div
            className='inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-7 w-7 md:h-8 md:w-8 p-0 cursor-pointer'
            title='Generate voiceover'
            onClick={e => {
              e.stopPropagation()
              onGenerateVoiceover()
            }}
          >
            <RotateCw
              className={`w-3 h-3 md:w-4 md:h-4 ${isGenerating ? 'animate-spin' : ''}`}
            />
          </div>
        )}

        {/* Play/Pause Button */}
        {!hideVoicePicker && scene.voiceSettings.voiceUrl && (
          <div
            className='inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-7 w-7 md:h-8 md:w-8 p-0 cursor-pointer'
            title={isPlaying ? 'Pause voiceover' : 'Play voiceover'}
            onClick={e => {
              e.stopPropagation()
              onPlayPause()
            }}
          >
            {isPlaying ? (
              <Pause className='w-3 h-3 md:w-4 md:h-4' />
            ) : (
              <Play className='w-3 h-3 md:w-4 md:h-4' />
            )}
          </div>
        )}
      </div>

      <div className='flex items-center gap-2 md:gap-3'>
        <Badge
          variant='secondary'
          className='text-xs md:text-sm px-2 md:px-3 py-1'
        >
          {formatDuration(duration)}
        </Badge>
        {!isPodcastOrAudioFlow &&
          (hasIssues ? (
            <AlertTriangle className='h-4 w-4 md:h-5 md:w-5 text-yellow-500' />
          ) : (
            <CheckCircle className='h-4 w-4 md:h-5 md:w-5 text-green-500' />
          ))}
      </div>
    </div>
  )
}
