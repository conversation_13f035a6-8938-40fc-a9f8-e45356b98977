'use client'

import { <PERSON>actNode, useRef, useState, useEffect } from 'react'
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'
import { VideoEditorHeader } from './video-editor-header'
import { Film, List } from 'lucide-react'

// Define the type for the fullscreen props
interface FullscreenProps {
  isFullscreen: boolean
  handleFullscreenToggle: () => void
  videoAreaRef: React.RefObject<HTMLDivElement | null>
}

interface VideoPreviewLayoutProps {
  children: [ReactNode, ReactNode | ((props: FullscreenProps) => ReactNode)]
  headerContent?: ReactNode
  mobileTab: 'editor' | 'preview'
  setMobileTab: (tab: 'editor' | 'preview') => void
}

export function VideoPreviewLayout({
  children,
  headerContent,
  mobileTab,
  setMobileTab,
}: VideoPreviewLayoutProps) {
  const videoAreaRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Track fullscreen state
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  const handleFullscreenToggle = () => {
    if (videoAreaRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen()
      } else {
        videoAreaRef.current.requestFullscreen().catch(err => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`)
        })
      }
    }
  }

  // Responsive layout: desktop = 40/60 split, mobile = toggle
  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width': '20rem',
          '--sidebar-width-mobile': '18rem',
        } as React.CSSProperties
      }
    >
      <SidebarInset>
        <div className='h-screen bg-background flex flex-col overflow-hidden'>
          {/* Header - Only show in non-fullscreen mode */}
          {!isFullscreen && (headerContent || <VideoEditorHeader />)}

          {/* Main Content Area */}
          <div className='flex-1 min-h-0 flex flex-row'>
            {/* Desktop: Left panel with minimum width, Mobile: show one panel at a time */}
            {/* Hide scenes sidebar in fullscreen mode */}
            {!isFullscreen && (
              <div className='hidden md:flex flex-col w-[400px] min-w-[400px] max-w-[500px] h-full overflow-y-auto border-r flex-shrink-0'>
                {children[0]}
              </div>
            )}
            <div
              className={`hidden md:flex flex-col ${isFullscreen ? 'w-full' : 'flex-1 min-w-0'} h-full`}
              ref={videoAreaRef}
              style={{
                zIndex: isFullscreen ? 30 : 'auto',
                overflow: isFullscreen ? 'hidden' : undefined,
              }}
            >
              {/* Pass fullscreen props to right panel if needed */}
              {typeof children[1] === 'function'
                ? (children[1] as (props: FullscreenProps) => ReactNode)({
                    isFullscreen,
                    handleFullscreenToggle,
                    videoAreaRef,
                  })
                : children[1]}
            </div>

            {/* Mobile: show only one panel at a time */}
            <div className='flex md:hidden flex-col w-full h-full min-h-0 overflow-y-auto pb-10'>
              {mobileTab === 'editor' ? children[0] : null}
              {mobileTab === 'preview' ? (
                <div
                  className='w-full h-full'
                  ref={videoAreaRef}
                  style={{
                    zIndex: isFullscreen ? 30 : 'auto',
                    overflow: isFullscreen ? 'hidden' : undefined,
                  }}
                >
                  {typeof children[1] === 'function'
                    ? (children[1] as (props: FullscreenProps) => ReactNode)({
                        isFullscreen,
                        handleFullscreenToggle,
                        videoAreaRef,
                      })
                    : children[1]}
                </div>
              ) : null}
            </div>
          </div>

          {/* Mobile Bottom Nav - Hide in fullscreen */}
          {!isFullscreen && (
            <div className='md:hidden fixed bottom-0 left-0 w-full bg-card border-t flex z-40 shadow-[0_-2px_12px_rgba(0,0,0,0.08)]'>
              <button
                className={`flex-1 flex items-center justify-center gap-2 py-3 text-base font-medium transition-colors duration-200 ${mobileTab === 'editor' ? 'text-primary bg-muted/60' : 'text-muted-foreground bg-transparent'} rounded-none`}
                onClick={() => setMobileTab('editor')}
                style={{ borderTopLeftRadius: 16 }}
              >
                <List className='h-5 w-5' />
                <span>Scene Editor</span>
              </button>
              <button
                className={`flex-1 flex items-center justify-center gap-2 py-3 text-base font-medium transition-colors duration-200 ${mobileTab === 'preview' ? 'text-primary bg-muted/60' : 'text-muted-foreground bg-transparent'} rounded-none`}
                onClick={() => setMobileTab('preview')}
                style={{ borderTopRightRadius: 16 }}
              >
                <Film className='h-5 w-5' />
                <span>Video Preview</span>
              </button>
            </div>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
