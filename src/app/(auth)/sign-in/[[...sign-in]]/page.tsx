import { SignIn } from '@clerk/nextjs'
import { Metadata } from 'next'
import { Suspense } from 'react'

export const metadata: Metadata = {
  title: 'Sign In - Adori AI',
  description: 'Sign in to your Adori AI account',
  robots: 'noindex, nofollow',
}

// Enable static generation for faster loading
export const dynamic = 'force-static'
export const revalidate = 3600 // Revalidate every hour

// Loading component to prevent layout shift
function SignInSkeleton() {
  return (
    <div className='w-full max-w-md mx-auto'>
      <div className='animate-pulse'>
        <div className='bg-muted h-8 w-32 mx-auto mb-6 rounded'></div>
        <div className='space-y-4'>
          <div className='bg-muted h-12 w-full rounded'></div>
          <div className='bg-muted h-12 w-full rounded'></div>
          <div className='bg-muted h-12 w-full rounded'></div>
        </div>
      </div>
    </div>
  )
}

export default function SignInPage() {
  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <Suspense fallback={<SignInSkeleton />}>
        <SignIn />
      </Suspense>
    </div>
  )
}
