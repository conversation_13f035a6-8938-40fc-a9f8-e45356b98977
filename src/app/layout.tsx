import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from '@/components/theme-provider'
import { ClerkThemeProvider } from '@/components/clerk-theme-provider'
import { ModalProvider } from '@/providers/modal-provider'
import { QueryProvider } from '@/providers/query-provider'
import { PrefetchProvider } from '@/providers/prefetch-provider'
import { Toaster } from '@/components/ui/toaster'
import { GlobalPageTransitionLoader } from '@/components/ui/page-transition-loader'
import { cn } from '@/lib/utils'
import { Suspense } from 'react'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Adori AI - Create Amazing Videos with AI',
  description:
    'Create professional videos from text, blog posts, PDFs, and more using AI.',
  icons: {
    icon: [
      {
        url: '/favicon.ico',
        sizes: 'any',
      },
      {
        url: '/icon.png',
        type: 'image/png',
        sizes: '32x32',
      },
      {
        url: '/apple-icon.png',
        type: 'image/png',
        sizes: '180x180',
      },
    ],
    apple: [
      {
        url: '/apple-icon.png',
        sizes: '180x180',
      },
    ],
  },
  manifest: '/site.webmanifest',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          inter.className
        )}
      >
        <QueryProvider>
          <PrefetchProvider>
            <ThemeProvider
              attribute='class'
              defaultTheme='system'
              enableSystem
              disableTransitionOnChange
            >
              <ClerkThemeProvider>
                <Suspense fallback={null}>
                  <GlobalPageTransitionLoader />
                </Suspense>
                {children}
                <ModalProvider />
                <Toaster richColors />
              </ClerkThemeProvider>
            </ThemeProvider>
          </PrefetchProvider>
        </QueryProvider>
      </body>
    </html>
  )
}
