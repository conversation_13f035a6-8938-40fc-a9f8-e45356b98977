@import 'tailwindcss';
@import 'tw-animate-css';
@import 'flag-icons/css/flag-icons.min.css';
@import '../components/editor/themes/editor-theme.css';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1408 0.0044 285.8229);
  --card: oklch(0.9846 0.0017 247.8389);
  --card-foreground: oklch(0.1408 0.0044 285.8229);
  --popover: oklch(0.9846 0.0017 247.8389);
  --popover-foreground: oklch(0.1408 0.0044 285.8229);
  --primary: oklch(0.8493 0.2073 128.8497);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.8711 0.0055 286.286);
  --secondary-foreground: oklch(0.1408 0.0044 285.8229);
  --muted: oklch(0.9674 0.0013 286.3752);
  --muted-foreground: oklch(0.5555 0 0);
  --accent: oklch(0.8493 0.2073 128.8497);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.5771 0.2152 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9197 0.004 286.3202);
  --input: oklch(0.9846 0.0017 247.8389);
  --ring: oklch(0.8493 0.2073 128.8497);
  --chart-1: oklch(0.8493 0.2073 128.8497);
  --chart-2: oklch(0.8452 0.1299 164.9782);
  --chart-3: oklch(0.6231 0.188 259.8145);
  --chart-4: oklch(0.5858 0.222 17.5846);
  --chart-5: oklch(0.6056 0.2189 292.7172);
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.1408 0.0044 285.8229);
  --sidebar-primary: oklch(0.8493 0.2073 128.8497);
  --sidebar-primary-foreground: oklch(0 0 0);
  --sidebar-accent: oklch(0.8493 0.2073 128.8497);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.9197 0.004 286.3202);
  --sidebar-ring: oklch(0.8493 0.2073 128.8497);
  --font-sans:
    Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
    Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-mono: Menlo, Monaco, Consolas, 'Courier New', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 5px 25px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 5px 25px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 5px 25px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0.025em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1408 0.0044 285.8229);
  --foreground: oklch(1 0 0);
  --card: oklch(0.2059 0.0059 285.871);
  --card-foreground: oklch(1 0 0);
  --popover: oklch(0.2059 0.0059 285.871);
  --popover-foreground: oklch(1 0 0);
  --primary: oklch(0.8493 0.2073 128.8497);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.4419 0.0146 285.7864);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.2707 0.0092 285.7705);
  --muted-foreground: oklch(0.7118 0.0129 286.0665);
  --accent: oklch(0.8493 0.2073 128.8497);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.3703 0.0119 285.8054);
  --input: oklch(0.2059 0.0059 285.871);
  --ring: oklch(0.8493 0.2073 128.8497);
  --chart-1: oklch(0.8493 0.2073 128.8497);
  --chart-2: oklch(0.8452 0.1299 164.9782);
  --chart-3: oklch(0.6231 0.188 259.8145);
  --chart-4: oklch(0.5858 0.222 17.5846);
  --chart-5: oklch(0.6056 0.2189 292.7172);
  --sidebar: oklch(0.2059 0.0059 285.871);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.8493 0.2073 128.8497);
  --sidebar-primary-foreground: oklch(0 0 0);
  --sidebar-accent: oklch(0.8493 0.2073 128.8497);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(0.3703 0.0119 285.8054);
  --sidebar-ring: oklch(0.8493 0.2073 128.8497);
  --font-sans:
    Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
    Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  --font-serif: ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-mono: Menlo, Monaco, Consolas, 'Courier New', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 5px 25px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 5px 25px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0px 5px 25px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 5px 25px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

@layer utilities {
  .animation-delay-150 {
    animation-delay: 150ms;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }

  .animation-delay-500 {
    animation-delay: 500ms;
  }

  /* Page transition shimmer effect */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
      opacity: 0.6;
    }
    50% {
      opacity: 1;
      transform: translateX(0%);
    }
    100% {
      transform: translateX(100%);
      opacity: 0.6;
    }
  }

  .animate-shimmer {
    animation: shimmer 1.5s ease-in-out infinite;
  }

  /* Alternative progress bar animation */
  @keyframes progressFill {
    0% {
      width: 0%;
      opacity: 1;
    }
    70% {
      width: 85%;
      opacity: 1;
    }
    90% {
      width: 95%;
      opacity: 1;
    }
    100% {
      width: 100%;
      opacity: 0;
    }
  }

  .animate-progress {
    animation: progressFill 3s ease-out forwards;
  }

  /* Performance-optimized animations */
  @keyframes fadeInWithDelay {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes progress {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes pulse-subtle {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .animate-fade-in-delayed {
    opacity: 0;
    animation: fadeInWithDelay 300ms 100ms forwards;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-progress {
    animation: progress 1.5s infinite;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 2s infinite;
  }

  /* Hardware acceleration for smooth animations */
  .animate-shimmer,
  .animate-progress,
  .animate-pulse-subtle,
  .animate-fade-in-delayed {
    will-change: transform, opacity;
    transform: translateZ(0);
  }

  /* Clerk pricing table specific optimizations */
  @keyframes skeleton-wave {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .clerk-skeleton-wave {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    background-size: 200px 100%;
    animation: skeleton-wave 1.5s infinite;
  }

  /* Smooth transitions for Clerk pricing table loading */
  .clerk-pricing-transition {
    transition:
      opacity 0.3s ease-in-out,
      transform 0.3s ease-in-out;
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .animate-shimmer,
    .animate-progress,
    .animate-pulse-subtle,
    .animate-fade-in-delayed,
    .clerk-skeleton-wave {
      animation: none;
    }

    .clerk-pricing-transition {
      transition: none;
    }
  }
}

/* Hide Clerk Delete Account button and section title in user profile modal (reliable selector) */
button[data-localization-key='userProfile.start.dangerSection.deleteAccountButton'] {
  display: none !important;
}

p[data-localization-key='userProfile.start.dangerSection.title'] {
  display: none !important;
}
