import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader } from '@/components/ui/card'

export default function MyVideosLoading() {
  return (
    <div className='max-w-7xl mx-auto px-4 py-6 space-y-6'>
      {/* Header Section */}
      <div className='flex items-center justify-between'>
        <div className='space-y-2'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-5 w-64' />
        </div>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-10 w-10' />
          <Skeleton className='h-10 w-10' />
        </div>
      </div>

      {/* Search and Filters */}
      <div className='flex items-center gap-4'>
        <Skeleton className='h-10 flex-1 max-w-sm' />
        <Skeleton className='h-10 w-32' />
        <Skeleton className='h-10 w-24' />
      </div>

      {/* Videos Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
        {Array.from({ length: 12 }).map((_, index) => (
          <Card key={index} className='group cursor-pointer'>
            <CardHeader className='p-0'>
              <div className='relative'>
                <Skeleton className='aspect-video w-full rounded-t-lg' />
                {/* Play button overlay */}
                <div className='absolute inset-0 flex items-center justify-center'>
                  <Skeleton className='h-12 w-12 rounded-full' />
                </div>
              </div>
            </CardHeader>
            <CardContent className='p-4 space-y-3'>
              <div className='space-y-2'>
                <Skeleton className='h-5 w-full' />
                <Skeleton className='h-4 w-20' />
              </div>
              
              <div className='flex items-center justify-between'>
                <Skeleton className='h-4 w-16' />
                <div className='flex items-center gap-1'>
                  <Skeleton className='h-6 w-6' />
                  <Skeleton className='h-6 w-6' />
                  <Skeleton className='h-6 w-6' />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
