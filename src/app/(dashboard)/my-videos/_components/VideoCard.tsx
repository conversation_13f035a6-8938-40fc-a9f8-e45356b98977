import { useRef, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Trash2,
  Play,
  Pause,
  Youtube,
  Download,
  Loader2,
  AlertCircle,
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface VideoCardProps {
  thumbnail: string | null
  created_at: Date
  url: string | null
  youtubeId: string | null
  onPublish: () => void
  status?: 'initializing' | 'rendering' | 'completed' | 'failed'
  progress?: number
  errorMessage?: string
  exportName?: string
  exportResolution?: string
  renderJobId: string
  onDeleted?: () => void
}

export function VideoCard({
  thumbnail,
  created_at,
  url,
  youtubeId,
  onPublish,
  status = 'completed',
  progress = 100,
  errorMessage,
  exportName,
  exportResolution,
  renderJobId,
  onDeleted,
}: VideoCardProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [playing, setPlaying] = useState(false)
  const [isLoadingDownload, setIsLoadingDownload] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handlePlayPause = () => {
    if (!videoRef.current) return
    if (playing) {
      videoRef.current.pause()
      setPlaying(false)
    } else {
      videoRef.current.play()
      setPlaying(true)
    }
  }

  const handleVideoEnded = () => {
    setPlaying(false)
  }

  // Helper function to get signed download URL
  const getDownloadUrl = async () => {
    if (!url) return null

    // Extract path from GCS URL
    // https://storage.googleapis.com/remotioncloudrun-iexx606ijk/renders/userId/renderId.mp4
    // Extract path from AWS URL
    // https://remotionlambda-useast1-klca987fje.s3.us-east-1.amazonaws.com/renders/userId/renderId.mp4

    const filename = exportName
      ? `${exportName}.mp4`
      : `video-${new Date().toISOString().split('T')[0]}.mp4`

    try {
      // Check if it's a GCS URL
      const gcsMatch = url.match(
        /https:\/\/storage\.googleapis\.com\/[^\/]+\/(.+)/
      )
      if (gcsMatch) {
        const filePath = gcsMatch[1]
        const response = await fetch(
          `/api/download-gcp-video/${filePath}?filename=${encodeURIComponent(filename)}`
        )
        if (!response.ok) throw new Error('Failed to get GCS download URL')
        const data = await response.json()
        return data.downloadUrl
      }

      // Check if it's an AWS S3 URL
      const awsMatch = url.match(
        /https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/
      )
      if (awsMatch) {
        const filePath = awsMatch[1]
        const response = await fetch(
          `/api/download-aws-video/${filePath}?filename=${encodeURIComponent(filename)}`
        )
        if (!response.ok) throw new Error('Failed to get AWS download URL')
        const data = await response.json()
        return data.downloadUrl
      }

      // Fallback to original URL
      return url
    } catch (error) {
      console.error('Error getting download URL:', error)
      return url // fallback to original URL
    }
  }

  const handleDownload = async () => {
    if (!url) return

    setIsLoadingDownload(true)
    try {
      const signedUrl = await getDownloadUrl()
      console.log('signedUrl', signedUrl)
      if (signedUrl) {
        const a = document.createElement('a')
        a.href = signedUrl
        a.download = exportName
          ? `${exportName}.mp4`
          : `video-${new Date().toISOString().split('T')[0]}.mp4`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        // fallback: open original video link in new tab
        window.open(url, '_blank')
      }
    } catch (error) {
      console.error('Download failed:', error)
      // fallback: open original video link in new tab
      window.open(url, '_blank')
    } finally {
      setIsLoadingDownload(false)
    }
  }

  const handleDelete = async () => {
    if (!renderJobId) return
    setIsDeleting(true)
    // Optimistically remove from UI
    onDeleted?.()
    try {
      // For failed exports, only delete from render job DB since there's no video file
      if (status === 'failed') {
        await fetch(`/api/delete-render-job/${renderJobId}`, {
          method: 'DELETE',
        })
        return
      }

      // For completed videos, delete both video file and render job
      if (!url) {
        throw new Error('No video URL available for deletion')
      }

      // Check if it's a GCS URL
      const gcsMatch = url.match(
        /https:\/\/storage\.googleapis\.com\/[^\/]+\/(.+)/
      )
      if (gcsMatch) {
        const filePath = gcsMatch[1]
        await fetch(`/api/delete-gcs-video/${filePath}?jobId=${renderJobId}`, {
          method: 'DELETE',
        })
        return
      }

      // Check if it's an AWS S3 URL
      const awsMatch = url.match(
        /https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/
      )
      if (awsMatch) {
        const filePath = awsMatch[1]
        await fetch(`/api/delete-aws-video/${filePath}?jobId=${renderJobId}`, {
          method: 'DELETE',
        })
        return
      }

      throw new Error('Invalid video URL - neither GCS nor AWS')
    } catch (err) {
      // Optionally: show a toast or alert
      console.error('Delete failed:', err)
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusOverlay = () => {
    if (status === 'completed') return null
    return (
      <div className='absolute inset-0 bg-black/60 flex flex-col items-center justify-center text-white z-20'>
        {status === 'rendering' && (
          <>
            <Loader2 className='w-8 h-8 animate-spin mb-2' />
            <span className='text-sm mb-2'>Rendering...</span>
            <div className='w-32 bg-gray-700 rounded-full h-2'>
              <div
                className='bg-white h-2 rounded-full transition-all duration-300'
                style={{ width: `${progress}%` }}
              />
            </div>
            <span className='text-xs mt-1'>{progress}%</span>
          </>
        )}
        {status === 'initializing' && (
          <>
            <Loader2 className='w-8 h-8 animate-spin mb-2' />
            <span className='text-sm'>Initializing...</span>
          </>
        )}
        {status === 'failed' && (
          <>
            <AlertCircle className='w-8 h-8 mb-2 text-red-400' />
            <span className='text-sm'>Export failed</span>
            {errorMessage && (
              <div className='text-xs text-red-200 bg-red-900/40 p-2 rounded mt-2 max-w-xs text-center'>
                {errorMessage}
              </div>
            )}
          </>
        )}
      </div>
    )
  }

  const getTimeText = () => {
    if (status === 'completed') {
      return `Exported ${formatDistanceToNow(created_at, { addSuffix: true })}`
    } else if (status === 'rendering') {
      return `Rendering... ${progress}%`
    } else if (status === 'failed') {
      return 'Export failed'
    } else {
      return 'Initializing...'
    }
  }

  return (
    <Card className='relative group overflow-hidden shadow hover:shadow-md transition-all duration-300 flex flex-col justify-between h-full'>
      {/* Delete button */}
      {(status === 'completed' || status === 'failed') && (
        <Button
          size='icon'
          variant='ghost'
          className='absolute top-2 right-2 z-30 bg-background/80 hover:bg-destructive/80 hover:text-destructive text-muted-foreground pointer-events-auto'
          onClick={handleDelete}
          aria-label='Delete video'
          disabled={isDeleting}
        >
          <Trash2 className='w-5 h-5' />
        </Button>
      )}

      {/* Video preview with status overlay */}
      <div className='relative aspect-video h-48 bg-black flex items-center justify-center'>
        <video
          ref={videoRef}
          src={url || undefined}
          poster={thumbnail || undefined}
          className='w-full h-full object-contain rounded-t-md bg-black'
          onEnded={handleVideoEnded}
        />
        {getStatusOverlay()}
        {/* Play button - only show for completed videos */}
        {status === 'completed' && (
          <Button
            size='icon'
            variant='secondary'
            className='absolute inset-0 m-auto w-12 h-12 rounded-full bg-background/80 hover:bg-primary/80 text-primary group-hover:scale-105 transition-transform duration-300 shadow-lg'
            onClick={handlePlayPause}
            aria-label={playing ? 'Pause' : 'Play'}
          >
            {playing ? (
              <Pause className='w-7 h-7' />
            ) : (
              <Play className='w-7 h-7' />
            )}
          </Button>
        )}
      </div>

      {/* Card content always at the bottom */}
      <div className='flex flex-col justify-end flex-1 px-4'>
        <div className='text-xs text-muted-foreground mb-2 whitespace-nowrap overflow-hidden text-ellipsis'>
          {getTimeText()}
        </div>
        {exportName && (
          <div className='text-xs text-muted-foreground mb-1'>
            <span className='font-semibold'>{exportName}</span>{' '}
            {exportResolution && <span>({exportResolution})</span>}
          </div>
        )}
        <div className='flex gap-2 flex-col'>
          {/* Download Video button */}
          <Button
            variant='outline'
            className='w-full'
            onClick={handleDownload}
            disabled={status !== 'completed' || !url || isLoadingDownload}
          >
            {isLoadingDownload ? (
              <Loader2 className='w-4 h-4 mr-2 animate-spin' />
            ) : (
              <Download className='w-4 h-4 mr-2' />
            )}
            {isLoadingDownload ? 'Preparing...' : 'Download Video'}
          </Button>
          {youtubeId ? (
            <Button asChild variant='default' className='w-full'>
              <a
                href={`https://youtube.com/watch?v=${youtubeId}`}
                target='_blank'
                rel='noopener noreferrer'
              >
                <Youtube className='w-4 h-4 mr-2' /> View on Youtube
              </a>
            </Button>
          ) : (
            <Button
              variant='default'
              className='w-full'
              onClick={onPublish}
              disabled={status !== 'completed' || !url}
            >
              <Youtube className='w-4 h-4 mr-2' /> Publish to Youtube
            </Button>
          )}
        </div>
      </div>
    </Card>
  )
}
