'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Settings } from 'lucide-react'
import { OrientationSelector, AutopickSelector } from './form-fields'
import { ClipPaceSelector as ClipPaceSelectorComponent } from './clip-pace-selector'

interface PodcastVideoConfigProps {
  orientation: 'landscape' | 'portrait' | 'square'
  onOrientationChange: (value: 'landscape' | 'portrait' | 'square') => void
  autopick: string
  onAutopickChange: (value: string) => void
  clipPace: 'fast' | 'medium' | 'slow' | 'verySlow'
  onClipPaceChange: (value: 'fast' | 'medium' | 'slow' | 'verySlow') => void
  disabled?: boolean
}

export function PodcastVideoConfig({
  orientation,
  onOrientationChange,
  autopick,
  onAutopickChange,
  clipPace,
  onClipPaceChange,
  disabled = false,
}: PodcastVideoConfigProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Settings className='w-4 h-4' />
          Video Configuration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='flex space-x-4 space-y-2 flex-wrap'>
          <OrientationSelector
            value={orientation}
            onValueChange={onOrientationChange}
            disabled={disabled}
          />

          <AutopickSelector
            value={autopick}
            onValueChange={onAutopickChange}
            disabled={disabled}
          />

          <ClipPaceSelectorComponent
            value={clipPace}
            onValueChange={onClipPaceChange}
            disabled={disabled}
          />
        </div>
      </CardContent>
    </Card>
  )
}
