'use client'

import { Suspense } from 'react'
import { Separator } from '@/components/ui/separator'
import { DurationSlider } from './duration-slider'
import { VoiceSelection } from './form-fields'
import {
  LanguageSelector,
  OrientationSelector,
  AutopickSelector,
} from './form-fields'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'

interface AdvancedSettingsProps {
  duration: number
  onDurationChange: (value: number) => void
  voice: ElevenVoice | null
  onVoiceSelect: (voice: ElevenVoice) => void
  language: string
  onLanguageChange: (value: string) => void
  orientation: 'landscape' | 'portrait' | 'square'
  onOrientationChange: (value: 'landscape' | 'portrait' | 'square') => void
  autopick: string
  onAutopickChange: (value: string) => void
  disabled?: boolean
}

export function AdvancedSettings({
  duration,
  onDurationChange,
  voice,
  onVoiceSelect,
  language,
  onLanguageChange,
  orientation,
  onOrientationChange,
  autopick,
  onAutopickChange,
  disabled,
}: AdvancedSettingsProps) {
  return (
    <>
      <Separator className='my-3' />

      {/* Advanced Settings - Suspense for progressive loading */}
      <Suspense
        fallback={<div className='h-32 animate-pulse bg-muted/20 rounded-md' />}
      >
        <div className='space-y-3'>
          {/* Duration and Voice Row */}
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
            <DurationSlider
              value={duration}
              onChange={onDurationChange}
              disabled={disabled}
            />

            <VoiceSelection
              voice={voice}
              onVoiceSelect={onVoiceSelect}
              disabled={disabled}
            />
          </div>

          {/* Language, Orientation, and Autopick Row */}
          <div className='flex space-x-4 space-y-2 flex-wrap '>
            <LanguageSelector
              value={language}
              onValueChange={onLanguageChange}
              disabled={disabled}
            />

            <OrientationSelector
              value={orientation}
              onValueChange={onOrientationChange}
              disabled={disabled}
            />

            <AutopickSelector
              value={autopick}
              onValueChange={onAutopickChange}
              disabled={disabled}
            />
          </div>
        </div>
      </Suspense>
    </>
  )
}
