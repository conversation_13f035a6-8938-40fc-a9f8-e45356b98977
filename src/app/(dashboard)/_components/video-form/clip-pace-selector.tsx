import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ClipPaceSelectorProps {
  value: 'fast' | 'medium' | 'slow' | 'verySlow'
  onValueChange: (value: 'fast' | 'medium' | 'slow' | 'verySlow') => void
  disabled?: boolean
  className?: string
}

export function ClipPaceSelector({
  value,
  onValueChange,
  disabled = false,
  className = '',
}: ClipPaceSelectorProps) {
  return (
    <div className={`flex-1 space-y-1 ${className}`}>
      <Label className='text-xs'>Clip pace</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className='h-8 text-xs'>
          <SelectValue placeholder='Clip pace' />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value='fast'>🐅 Fast (change every 5 sec)</SelectItem>
          <SelectItem value='medium'>🏃‍♂️ Medium (change every 8 sec)</SelectItem>
          <SelectItem value='slow'>🐢 Slow (change every 12 sec)</SelectItem>
          <SelectItem value='verySlow'>
            🐌 Very Slow (change every 15 sec)
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
