'use client'

import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
import { OptimizedVoicePicker } from './optimized-voice-picker'
import {
  TONE_OPTIONS,
  AUDIENCE_OPTIONS,
  PLATFORM_OPTIONS,
  LANGUAGE_OPTIONS,
  ORIENTATION_OPTIONS,
  AUTOPICK_OPTIONS,
} from './types'

interface FormFieldProps {
  disabled?: boolean
}

interface ToneSelectorProps extends FormFieldProps {
  value: string
  onValueChange: (value: string) => void
}

export function ToneSelector({
  value,
  onValueChange,
  disabled,
}: ToneSelectorProps) {
  return (
    <div className='space-y-1'>
      <Label className='text-sm'>Video Tone</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className='h-9 text-xs'>
          <SelectValue placeholder='Select tone' />
        </SelectTrigger>
        <SelectContent>
          {TONE_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

interface AudienceSelectorProps extends FormFieldProps {
  value: string
  onValueChange: (value: string) => void
}

export function AudienceSelector({
  value,
  onValueChange,
  disabled,
}: AudienceSelectorProps) {
  return (
    <div className='space-y-1'>
      <Label className='text-sm'>Target Audience</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className='h-9 text-xs'>
          <SelectValue placeholder='Select audience' />
        </SelectTrigger>
        <SelectContent>
          {AUDIENCE_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

interface PlatformSelectorProps extends FormFieldProps {
  value: string
  onValueChange: (value: string) => void
}

export function PlatformSelector({
  value,
  onValueChange,
  disabled,
}: PlatformSelectorProps) {
  return (
    <div className='space-y-1'>
      <Label className='text-sm'>Platform</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className='h-9 text-xs'>
          <SelectValue placeholder='Select platform' />
        </SelectTrigger>
        <SelectContent>
          {PLATFORM_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

interface IncludeOptionsProps extends FormFieldProps {
  includeHook: boolean
  includeCTA: boolean
  onIncludeHookChange: (checked: boolean) => void
  onIncludeCTAChange: (checked: boolean) => void
}

export function IncludeOptions({
  includeHook,
  includeCTA,
  onIncludeHookChange,
  onIncludeCTAChange,
  disabled,
}: IncludeOptionsProps) {
  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
      <div className='flex items-center space-x-2'>
        <Checkbox
          id='include-hook'
          checked={includeHook}
          onCheckedChange={checked => onIncludeHookChange(checked as boolean)}
          disabled={disabled}
        />
        <div className='grid gap-1.5 leading-none'>
          <Label
            htmlFor='include-hook'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Include Hook
          </Label>
          <p className='text-xs text-muted-foreground'>
            Attention-grabbing opening
          </p>
        </div>
      </div>

      <div className='flex items-center space-x-2'>
        <Checkbox
          id='include-cta'
          checked={includeCTA}
          onCheckedChange={checked => onIncludeCTAChange(checked as boolean)}
          disabled={disabled}
        />
        <div className='grid gap-1.5 leading-none'>
          <Label
            htmlFor='include-cta'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Include Call-to-Action
          </Label>
          <p className='text-xs text-muted-foreground'>Clear next step</p>
        </div>
      </div>
    </div>
  )
}

interface KeywordsInputProps extends FormFieldProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

export function KeywordsInput({
  value,
  onChange,
  disabled,
  placeholder,
}: KeywordsInputProps) {
  return (
    <div className='space-y-1'>
      <Label htmlFor='keywords' className='text-sm'>
        Keywords (optional)
      </Label>
      <Input
        id='keywords'
        placeholder={placeholder || 'e.g. technology, innovation, startup'}
        value={value}
        onChange={e => onChange(e.target.value)}
        className='h-9 text-sm'
        disabled={disabled}
      />
    </div>
  )
}

interface VoiceSelectionProps extends FormFieldProps {
  voice: ElevenVoice | null
  onVoiceSelect: (voice: ElevenVoice) => void
}

export function VoiceSelection({
  voice,
  onVoiceSelect,
  disabled,
}: VoiceSelectionProps) {
  return (
    <OptimizedVoicePicker
      voice={voice}
      onVoiceSelect={onVoiceSelect}
      disabled={disabled}
    />
  )
}

interface LanguageSelectorProps extends FormFieldProps {
  value: string
  onValueChange: (value: string) => void
}

export function LanguageSelector({
  value,
  onValueChange,
  disabled,
}: LanguageSelectorProps) {
  return (
    <div className='space-y-1'>
      <Label className='text-sm'>Language</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className='h-9 text-xs'>
          <SelectValue placeholder='Language' />
        </SelectTrigger>
        <SelectContent className='max-h-[300px] overflow-y-auto'>
          {LANGUAGE_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

interface OrientationSelectorProps extends FormFieldProps {
  value: 'landscape' | 'portrait' | 'square'
  onValueChange: (value: 'landscape' | 'portrait' | 'square') => void
  compact?: boolean
  className?: string
}

export function OrientationSelector({
  value,
  onValueChange,
  disabled,
  compact = false,
  className = '',
}: OrientationSelectorProps) {
  return (
    <div className={`${compact ? 'flex-1 space-y-1' : ''} ${className}`}>
      <Label className={compact ? 'text-xs' : 'text-sm'}>Orientation</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className={compact ? 'h-8 text-xs' : 'h-9 text-xs'}>
          <SelectValue placeholder='Orientation' />
        </SelectTrigger>
        <SelectContent>
          {ORIENTATION_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

interface AutopickSelectorProps extends FormFieldProps {
  value: string
  onValueChange: (value: string) => void
  compact?: boolean
  className?: string
}

export function AutopickSelector({
  value,
  onValueChange,
  disabled,
  compact = false,
  className = '',
}: AutopickSelectorProps) {
  return (
    <div className={`${compact ? 'flex-1 space-y-1' : ''} ${className}`}>
      <Label className={compact ? 'text-xs' : 'text-sm'}>Autopick</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger className={compact ? 'h-8 text-xs' : 'h-9 text-xs'}>
          <SelectValue
            placeholder={compact ? 'Media type' : 'Select media type'}
          />
        </SelectTrigger>
        <SelectContent>
          {AUTOPICK_OPTIONS.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
