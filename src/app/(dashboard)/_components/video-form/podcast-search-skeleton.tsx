import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Search } from 'lucide-react'

export function PodcastSearchSkeleton() {
  return (
    <Card>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-base'>
          <Search className='w-4 h-4' />
          Find Podcast
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-3'>
        {/* Tab buttons skeleton */}
        <div className='flex gap-1 p-1 bg-muted rounded-lg'>
          <Skeleton className='h-8 flex-1 rounded-md' />
          <Skeleton className='h-8 flex-1 rounded-md' />
        </div>

        {/* Search input area skeleton */}
        <div className='space-y-2'>
          <Skeleton className='h-4 w-32' />
          <div className='flex gap-2'>
            <Skeleton className='h-9 flex-1' />
            <Skeleton className='h-9 w-12' />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
