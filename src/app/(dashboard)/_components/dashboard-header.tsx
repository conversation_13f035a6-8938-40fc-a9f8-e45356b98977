'use client'

import { usePathname } from 'next/navigation'
import { PageHeader } from '@/components/ui/page-header'
import {
  Video,
  FileVideo,
  CreditCard,
  Settings,
  Lightbulb,
  Link,
  FileText,
  FileUp,
  AudioLines,
  Radio,
  FolderKanban,
  LucideIcon,
} from 'lucide-react'

// Icon mapping from string names to components
const ICON_MAP: Record<string, LucideIcon> = {
  Video,
  FileVideo,
  CreditCard,
  Settings,
  Lightbulb,
  Link,
  FileText,
  FileUp,
  AudioLines,
  Radio,
  FolderKanban,
}

interface DashboardHeaderProps {
  pageConfig: Record<string, { title: string; icon: string }>
  defaultConfig: { title: string; icon: string }
}

export function DashboardHeader({
  pageConfig,
  defaultConfig,
}: DashboardHeaderProps) {
  const pathname = usePathname()

  // Get page config from mapping or fallback to default
  const currentPageConfig = pageConfig[pathname] || defaultConfig

  // Convert string icon name to actual icon component
  const IconComponent = ICON_MAP[currentPageConfig.icon] || Video

  return <PageHeader title={currentPageConfig.title} icon={IconComponent} />
}
