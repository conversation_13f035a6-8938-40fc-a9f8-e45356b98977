import React from 'react'
import Link from 'next/link'
import { CardDescription, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  CardWithHover,
  CardWithHoverHeader,
  CardWithHoverFooter,
} from '@/components/ui/extended'

interface ConversionCardProps {
  title: string
  description: string
  icon: React.ElementType
  actionText: string
  href: string
}

const colorMap: Record<
  string,
  { bg: string; text: string; button: string; buttonHover: string }
> = {
  'Idea to Video': {
    bg: 'bg-blue-600 dark:bg-blue-500',
    text: 'text-white',
    button: 'bg-blue-600 dark:bg-blue-500 text-white border-0',
    buttonHover: 'hover:bg-blue-700 dark:hover:bg-blue-600',
  },
  'Blog to Video': {
    bg: 'bg-purple-600 dark:bg-purple-500',
    text: 'text-white',
    button: 'bg-purple-600 dark:bg-purple-500 text-white border-0',
    buttonHover: 'hover:bg-purple-700 dark:hover:bg-purple-600',
  },
  'Text to Video': {
    bg: 'bg-emerald-600 dark:bg-emerald-500',
    text: 'text-white',
    button: 'bg-emerald-600 dark:bg-emerald-500 text-white border-0',
    buttonHover: 'hover:bg-emerald-700 dark:hover:bg-emerald-600',
  },
  'PDF to Video': {
    bg: 'bg-orange-500 dark:bg-orange-400',
    text: 'text-white',
    button: 'bg-orange-500 dark:bg-orange-400 text-white border-0',
    buttonHover: 'hover:bg-orange-600 dark:hover:bg-orange-500',
  },
  'Audio to Video': {
    bg: 'bg-teal-600 dark:bg-teal-500',
    text: 'text-white',
    button: 'bg-teal-600 dark:bg-teal-500 text-white border-0',
    buttonHover: 'hover:bg-teal-700 dark:hover:bg-teal-600',
  },
  'Podcast to Video': {
    bg: 'bg-pink-600 dark:bg-pink-500',
    text: 'text-white',
    button: 'bg-pink-600 dark:bg-pink-500 text-white border-0',
    buttonHover: 'hover:bg-pink-700 dark:hover:bg-pink-600',
  },
}

const ConversionCard = ({
  title,
  description,
  icon: Icon,
  actionText,
  href,
}: ConversionCardProps) => {
  const color = colorMap[title] || colorMap['Idea to Video']
  return (
    <CardWithHover className='bg-card/60 border-border/60 hover:bg-card/90 hover:border-border/80 transition-all duration-300 ease-out backdrop-blur-sm'>
      <CardWithHoverHeader className='flex flex-col items-center justify-center pt-6 pb-2 px-6 '>
        <div className='flex items-center justify-center'>
          <div
            className={`rounded-full ${color.bg} ${color.text} flex items-center justify-center w-14 h-14 shadow-lg transition-all duration-300 group-hover:shadow-xl group-hover:scale-110`}
          >
            <Icon className='h-7 w-7 transition-transform duration-300 group-hover:scale-110' />
          </div>
        </div>
        <div className='space-y-3 text-center'>
          <CardTitle className='text-lg font-semibold leading-tight text-center px-2'>
            {title}
          </CardTitle>
          <CardDescription className='text-sm text-muted-foreground/90 leading-relaxed text-center px-2 min-h-[2.5rem] flex items-center justify-center'>
            {description}
          </CardDescription>
        </div>
      </CardWithHoverHeader>
      <CardWithHoverFooter className='px-6 pb-6 pt-2 flex justify-center'>
        <Button
          asChild
          className={`w-full max-w-[160px] h-11 font-semibold transition-all duration-300 shadow-sm hover:shadow-md ${color.button} ${color.buttonHover} hover:scale-105`}
        >
          <Link href={href}>{actionText}</Link>
        </Button>
      </CardWithHoverFooter>
    </CardWithHover>
  )
}

export default ConversionCard
