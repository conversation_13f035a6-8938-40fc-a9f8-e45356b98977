'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/lib/toast'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Loader2,
  Zap,
  Play,
  Download,
  Upload,
  Save,
  Trash2,
  Copy,
} from 'lucide-react'

export default function ToastDemoPage() {
  const [customMessage, setCustomMessage] = useState('')
  const [loadingToastId, setLoadingToastId] = useState<string | number | null>(
    null
  )

  // Basic toast demos
  const showSuccessToast = () => {
    toast.success('Operation completed successfully!')
  }

  const showErrorToast = () => {
    toast.error('Something went wrong. Please try again.')
  }

  const showWarningToast = () => {
    toast.warning('Please complete all required fields before proceeding.')
  }

  const showInfoToast = () => {
    toast.info('New features are now available in the settings panel.')
  }

  // Custom message toast
  const showCustomToast = () => {
    if (!customMessage.trim()) {
      toast.warning('Please enter a message first!')
      return
    }
    toast.success(customMessage)
    setCustomMessage('')
  }

  // Loading toast demo
  const showLoadingToast = () => {
    const toastId = toast.loading('Processing your request...')
    setLoadingToastId(toastId)

    // Simulate progress updates
    setTimeout(() => {
      toast.renderProgress('Analyzing data...', toastId)
    }, 1500)

    setTimeout(() => {
      toast.renderProgress('Finalizing results...', toastId)
    }, 3000)

    setTimeout(() => {
      toast.dismiss(toastId)
      toast.success('Processing complete!')
      setLoadingToastId(null)
    }, 4500)
  }

  // Dismiss loading toast manually
  const dismissLoadingToast = () => {
    if (loadingToastId) {
      toast.dismiss(loadingToastId)
      setLoadingToastId(null)
    }
  }

  // Promise toast demo
  const showPromiseToast = () => {
    const mockApiCall = () =>
      new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() > 0.3) {
            resolve('Success!')
          } else {
            reject(new Error('Network error'))
          }
        }, 2000)
      })

    return toast.promise(mockApiCall(), {
      loading: 'Uploading file...',
      success: 'File uploaded successfully!',
      error: 'Failed to upload file',
    })
  }

  // API error demo
  const showApiErrorToast = () => {
    const mockError = {
      response: {
        data: {
          message: 'Invalid API key. Please check your credentials.',
        },
      },
    }
    toast.apiError(mockError)
  }

  // Specialized toasts
  const showCaptionTemplateToast = () => {
    toast.captionTemplateSuccess('Modern Gradient Style')
  }

  const showRenderProgressToast = () => {
    const toastId = toast.renderProgress('Starting video render...')

    setTimeout(() => {
      toast.renderProgress('Rendering scenes (1/5)...', toastId)
    }, 1000)

    setTimeout(() => {
      toast.renderProgress('Adding effects (3/5)...', toastId)
    }, 2500)

    setTimeout(() => {
      toast.renderProgress('Finalizing video (5/5)...', toastId)
    }, 4000)

    setTimeout(() => {
      toast.dismiss(toastId)
      toast.success('Video rendered and ready for download!')
    }, 5500)
  }

  // Toast with actions
  const showActionToast = () => {
    toast.error('Failed to save changes', {
      action: {
        label: 'Retry',
        onClick: () => {
          toast.success('Retrying save operation...')
        },
      },
    })
  }

  // Persistent toast
  const showPersistentToast = () => {
    toast.info('This notification will stay until you dismiss it manually.', {
      duration: Infinity,
    })
  }

  // Multiple toasts
  const showMultipleToasts = () => {
    toast.info('Starting batch operation...')
    setTimeout(() => toast.success('File 1 processed'), 500)
    setTimeout(() => toast.success('File 2 processed'), 1000)
    setTimeout(() => toast.success('File 3 processed'), 1500)
    setTimeout(() => toast.success('All files processed successfully!'), 2000)
  }

  // Real-world scenarios
  const simulateVideoRender = () => {
    if (Math.random() > 0.5) {
      toast.warning('Please add scenes with voiceover before rendering')
      return
    }

    const renderToast = toast.renderProgress('Preparing video...')

    setTimeout(() => {
      toast.renderProgress('Rendering video...', renderToast)
    }, 1000)

    setTimeout(() => {
      toast.renderProgress('Downloading...', renderToast)
    }, 3000)

    setTimeout(() => {
      toast.dismiss(renderToast)
      toast.success('Video rendered and downloaded successfully!')
    }, 4000)
  }

  const simulateVoiceGeneration = () => {
    return toast.promise(
      new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() > 0.2) {
            resolve('Generated')
          } else {
            reject(new Error('Voice generation failed'))
          }
        }, 2500)
      }),
      {
        loading: 'Generating voiceover...',
        success: 'Voiceover generated successfully!',
        error: 'Failed to generate voiceover',
      }
    )
  }

  const simulateFileUpload = () => {
    const files = ['video.mp4', 'audio.mp3', 'image.jpg']

    files.forEach((filename, index) => {
      setTimeout(() => {
        toast.promise(new Promise(resolve => setTimeout(resolve, 1500)), {
          loading: `Uploading ${filename}...`,
          success: `${filename} uploaded successfully!`,
          error: `Failed to upload ${filename}`,
        })
      }, index * 300)
    })
  }

  const dismissAllToasts = () => {
    toast.dismissAll()
  }

  return (
    <div className='container mx-auto p-6 max-w-6xl'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold mb-2'>Toast Notification Demo</h1>
        <p className='text-muted-foreground'>
          Test all the different types of toast notifications available in the
          Adori AI platform.
        </p>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Basic Toast Types */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <CheckCircle className='h-5 w-5 text-green-500' />
              Basic Toast Types
            </CardTitle>
            <CardDescription>
              Standard notification types for different scenarios
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <Button
              onClick={showSuccessToast}
              className='w-full justify-start'
              variant='outline'
            >
              <CheckCircle className='h-4 w-4 mr-2 text-green-500' />
              Success Toast
            </Button>
            <Button
              onClick={showErrorToast}
              className='w-full justify-start'
              variant='outline'
            >
              <XCircle className='h-4 w-4 mr-2 text-red-500' />
              Error Toast
            </Button>
            <Button
              onClick={showWarningToast}
              className='w-full justify-start'
              variant='outline'
            >
              <AlertTriangle className='h-4 w-4 mr-2 text-yellow-500' />
              Warning Toast
            </Button>
            <Button
              onClick={showInfoToast}
              className='w-full justify-start'
              variant='outline'
            >
              <Info className='h-4 w-4 mr-2 text-blue-500' />
              Info Toast
            </Button>
          </CardContent>
        </Card>

        {/* Advanced Toast Features */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Zap className='h-5 w-5 text-purple-500' />
              Advanced Features
            </CardTitle>
            <CardDescription>
              Loading states, promises, and interactive toasts
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <Button
              onClick={showLoadingToast}
              className='w-full justify-start'
              variant='outline'
              disabled={!!loadingToastId}
            >
              <Loader2 className='h-4 w-4 mr-2' />
              Loading Toast with Progress
            </Button>
            {loadingToastId && (
              <Button
                onClick={dismissLoadingToast}
                className='w-full justify-start'
                variant='destructive'
                size='sm'
              >
                <XCircle className='h-4 w-4 mr-2' />
                Dismiss Loading Toast
              </Button>
            )}
            <Button
              onClick={showPromiseToast}
              className='w-full justify-start'
              variant='outline'
            >
              <Upload className='h-4 w-4 mr-2' />
              Promise Toast (50/50 success)
            </Button>
            <Button
              onClick={showActionToast}
              className='w-full justify-start'
              variant='outline'
            >
              <Copy className='h-4 w-4 mr-2' />
              Toast with Action Button
            </Button>
            <Button
              onClick={showPersistentToast}
              className='w-full justify-start'
              variant='outline'
            >
              <Info className='h-4 w-4 mr-2' />
              Persistent Toast
            </Button>
          </CardContent>
        </Card>

        {/* Custom Message */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Message</CardTitle>
            <CardDescription>Test with your own message</CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='space-y-2'>
              <Label htmlFor='custom-message'>Your Message</Label>
              <Input
                id='custom-message'
                value={customMessage}
                onChange={e => setCustomMessage(e.target.value)}
                placeholder='Enter a custom message...'
                onKeyDown={e => e.key === 'Enter' && showCustomToast()}
              />
            </div>
            <Button onClick={showCustomToast} className='w-full'>
              <Play className='h-4 w-4 mr-2' />
              Show Custom Toast
            </Button>
          </CardContent>
        </Card>

        {/* Specialized Toasts */}
        <Card>
          <CardHeader>
            <CardTitle>Specialized Toasts</CardTitle>
            <CardDescription>App-specific toast functions</CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <Button
              onClick={showCaptionTemplateToast}
              className='w-full justify-start'
              variant='outline'
            >
              <Save className='h-4 w-4 mr-2' />
              Caption Template Success
            </Button>
            <Button
              onClick={showRenderProgressToast}
              className='w-full justify-start'
              variant='outline'
            >
              <Download className='h-4 w-4 mr-2' />
              Video Render Progress
            </Button>
            <Button
              onClick={showApiErrorToast}
              className='w-full justify-start'
              variant='outline'
            >
              <XCircle className='h-4 w-4 mr-2' />
              API Error Handler
            </Button>
          </CardContent>
        </Card>

        {/* Real-world Scenarios */}
        <Card className='lg:col-span-2'>
          <CardHeader>
            <CardTitle>Real-world Scenarios</CardTitle>
            <CardDescription>
              Simulate actual app workflows with toast notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3'>
              <Button
                onClick={simulateVideoRender}
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
              >
                <Play className='h-6 w-6' />
                <div className='text-center'>
                  <div className='font-medium'>Video Render</div>
                  <div className='text-xs text-muted-foreground'>
                    Complete workflow
                  </div>
                </div>
              </Button>

              <Button
                onClick={simulateVoiceGeneration}
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
              >
                <Loader2 className='h-6 w-6' />
                <div className='text-center'>
                  <div className='font-medium'>Voice Generation</div>
                  <div className='text-xs text-muted-foreground'>
                    Promise-based
                  </div>
                </div>
              </Button>

              <Button
                onClick={simulateFileUpload}
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
              >
                <Upload className='h-6 w-6' />
                <div className='text-center'>
                  <div className='font-medium'>File Upload</div>
                  <div className='text-xs text-muted-foreground'>
                    Multiple files
                  </div>
                </div>
              </Button>

              <Button
                onClick={showMultipleToasts}
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
              >
                <Zap className='h-6 w-6' />
                <div className='text-center'>
                  <div className='font-medium'>Batch Operation</div>
                  <div className='text-xs text-muted-foreground'>
                    Sequential toasts
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Toast Controls */}
        <Card className='lg:col-span-2'>
          <CardHeader>
            <CardTitle>Toast Controls</CardTitle>
            <CardDescription>Manage active toasts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='flex gap-3'>
              <Button onClick={dismissAllToasts} variant='destructive'>
                <Trash2 className='h-4 w-4 mr-2' />
                Dismiss All Toasts
              </Button>
              <Badge variant='secondary' className='px-3 py-1'>
                Rich Colors Enabled
              </Badge>
              <Badge variant='outline' className='px-3 py-1'>
                Theme Adaptive
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator className='my-8' />

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Examples</CardTitle>
          <CardDescription>
            Code examples for implementing these toasts in your components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4 text-sm'>
            <div className='bg-muted p-4 rounded-lg'>
              <div className='font-medium mb-2'>Basic Usage:</div>
              <code className='text-green-600'>
                toast.success(&apos;Operation completed!&apos;)
              </code>
              <br />
              <code className='text-red-600'>
                toast.error(&apos;Something went wrong&apos;)
              </code>
              <br />
              <code className='text-yellow-600'>
                toast.warning(&apos;Please check your input&apos;)
              </code>
              <br />
              <code className='text-blue-600'>
                toast.info(&apos;New feature available&apos;)
              </code>
            </div>

            <div className='bg-muted p-4 rounded-lg'>
              <div className='font-medium mb-2'>Promise Toast:</div>
              <code className='block'>
                {`toast.promise(apiCall(), {
  loading: 'Processing...',
  success: 'Done!',
  error: 'Failed!'
})`}
              </code>
            </div>

            <div className='bg-muted p-4 rounded-lg'>
              <div className='font-medium mb-2'>Loading with Progress:</div>
              <code className='block'>
                {`const id = toast.renderProgress('Starting...')
toast.renderProgress('Processing...', id)
toast.dismiss(id)`}
              </code>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
