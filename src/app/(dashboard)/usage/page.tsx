const usageData = [
  { name: 'No of Projects', max: 120, used: 120 },
  { name: 'No of video exports', max: 3, used: 1 },
  { name: 'AI image generation', max: 100, used: 50 },
]

export default function UsagePage() {
  return (
    <div className='container mx-auto p-6'>
      <div className='bg-card p-8 rounded-lg shadow-md max-w-2xl mx-auto'>
        <table className='w-full text-left'>
          <thead>
            <tr>
              <th className='pb-3 text-muted-foreground font-medium'>
                Usage Type
              </th>
            </tr>
          </thead>
          <tbody>
            {usageData.map(item => (
              <tr key={item.name} className='border-b last:border-b-0'>
                <td className='py-3'>
                  <div className='flex flex-col gap-1'>
                    <span className='font-medium'>{item.name}</span>
                    <span className='text-xs text-muted-foreground font-semibold text-center'>
                      {item.used}/{item.max}
                    </span>
                    <div className='w-full bg-gray-200 dark:bg-gray-800 rounded-full h-2 mt-1'>
                      <div
                        className='h-2 rounded-full transition-all duration-300'
                        style={{
                          width: `${(item.used / item.max) * 100}%`,
                          background:
                            item.used === 0
                              ? 'linear-gradient(90deg, #a1a1aa 0%, #d4d4d8 100%)'
                              : 'linear-gradient(90deg, #6366f1 0%, #06b6d4 100%)',
                        }}
                      />
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
