import { ClerkPricingWrapperUltimate } from '@/components/clerk-pricing-wrapper'
import type { Metadata } from 'next'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 10800

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'Pricing Plans - Adori AI',
  description:
    'Choose the perfect plan for your AI video creation needs. All plans include access to our powerful video creation tools.',
  keywords: [
    'pricing',
    'plans',
    'AI video',
    'content creation',
    'subscription',
  ],
  openGraph: {
    title: 'Pricing Plans - Adori AI',
    description: 'Choose the perfect plan for your AI video creation needs.',
    type: 'website',
  },
}

export default function PricingPage() {
  return (
    <div className='max-w-4xl mx-auto py-12 px-4 sm:px-6'>
      {/* Page header - statically generated */}
      <header className='mb-12 text-center'>
        <h1 className='text-3xl font-bold mb-3'>Choose your plan</h1>
        <p className='text-muted-foreground max-w-2xl mx-auto'>
          Select the perfect plan for your content creation needs. All plans
          include access to our AI video creation tools.
        </p>
      </header>

      {/* Cached Clerk Pricing Table */}
      <section className='mb-16' aria-label='Pricing plans'>
        <ClerkPricingWrapperUltimate />
      </section>

      {/* Enterprise Contact Section */}
      <div className='w-full mx-auto rounded-lg bg-card border border-border/40 p-8 text-center'>
        <div className='max-w-lg mx-auto'>
          <h2 className='text-xl font-semibold mb-3'>
            Need a custom solution?
          </h2>
          <p className='text-muted-foreground mb-6 text-sm'>
            Our team can help you find the perfect plan for your content
            creation needs. We offer custom solutions for enterprise customers
            with special requirements.
          </p>
          <a
            href='mailto:<EMAIL>'
            className='inline-flex items-center gap-1.5 text-primary hover:text-primary/90 font-medium'
          >
            Contact our sales team
            <svg
              className='h-4 w-4'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 5l7 7-7 7'
              />
            </svg>
          </a>
        </div>
      </div>
    </div>
  )
}
