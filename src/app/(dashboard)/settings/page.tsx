import { ThemeToggle } from '@/components/theme-toggle'
import { AccountSettings } from './_components/account-settings'
import { ApiKeys } from './_components/api-keys'

export default function SettingsPage() {
  return (
    <div className='container mx-auto p-6'>
      <div className='space-y-6'>
        <div className='bg-card rounded-lg p-6'>
          <h2 className='text-xl font-semibold mb-4'>Appearance</h2>
          <div className='flex items-center justify-between'>
            <div>
              <p className='font-medium'>Theme</p>
              <p className='text-sm text-muted-foreground'>
                Customize the appearance of the application
              </p>
            </div>
            <ThemeToggle />
          </div>
        </div>

        <AccountSettings />
        <ApiKeys />
      </div>
    </div>
  )
}
