'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Copy, Plus, Trash2 } from 'lucide-react'
import { toast } from '@/lib/toast'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

interface ApiKey {
  id: string
  name: string
  key: string
  lastUsed: string | null
  createdAt: string
  permissions: 'all' | 'read'
}

export function ApiKeys() {
  const [isCreating, setIsCreating] = useState(false)
  const [newKeyName, setNewKeyName] = useState('')
  const [newKeyPermissions, setNewKeyPermissions] = useState<'all' | 'read'>(
    'all'
  )
  const queryClient = useQueryClient()

  // Fetch API keys
  const { data: apiKeys = [], isLoading } = useQuery<ApiKey[]>({
    queryKey: ['apiKeys'],
    queryFn: async () => {
      const response = await fetch('/api/keys')
      if (!response.ok) {
        throw new Error('Failed to fetch API keys')
      }
      return response.json()
    },
  })

  // Create API key mutation
  const createKeyMutation = useMutation({
    mutationFn: async (data: { name: string; permissions: 'all' | 'read' }) => {
      const response = await fetch('/api/keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      if (!response.ok) {
        throw new Error('Failed to create API key')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apiKeys'] })
      setNewKeyName('')
      setNewKeyPermissions('all')
      setIsCreating(false)
      toast.success('API key created successfully')
    },
    onError: () => {
      toast.error('Failed to create API key')
    },
  })

  // Delete API key mutation
  const deleteKeyMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/keys?id=${id}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        throw new Error('Failed to delete API key')
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apiKeys'] })
      toast.success('API key deleted successfully')
    },
    onError: () => {
      toast.error('Failed to delete API key')
    },
  })

  const handleCreateKey = async () => {
    if (!newKeyName.trim()) {
      toast.error('Please enter a name for the API key')
      return
    }

    createKeyMutation.mutate({
      name: newKeyName,
      permissions: newKeyPermissions,
    })
  }

  const handleDeleteKey = async (id: string) => {
    deleteKeyMutation.mutate(id)
  }

  const handleCopyKey = (key: string) => {
    navigator.clipboard.writeText(key)
    toast.success('API key copied to clipboard')
  }

  return (
    <div className='bg-card rounded-lg p-6'>
      <div className='flex items-center justify-between mb-6'>
        <div>
          <h2 className='text-xl font-semibold'>API Keys</h2>
          <p className='text-sm text-muted-foreground mt-1'>
            Manage your API keys for programmatic access
          </p>
        </div>
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button>
              <Plus className='w-4 h-4 mr-2' />
              Create API Key
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New API Key</DialogTitle>
            </DialogHeader>
            <div className='space-y-4 py-4'>
              <div className='space-y-2'>
                <label className='text-sm font-medium'>Name</label>
                <Input
                  placeholder='Enter API key name'
                  value={newKeyName}
                  onChange={e => setNewKeyName(e.target.value)}
                />
              </div>
              <div className='space-y-2'>
                <label className='text-sm font-medium'>Permissions</label>
                <Select
                  value={newKeyPermissions}
                  onValueChange={value =>
                    setNewKeyPermissions(value as 'all' | 'read')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>All</SelectItem>
                    <SelectItem value='read'>Read Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='flex justify-end gap-2'>
                <Button variant='outline' onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateKey}
                  disabled={createKeyMutation.isPending}
                >
                  {createKeyMutation.isPending ? 'Creating...' : 'Create Key'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Key</TableHead>
              <TableHead>Last Used</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Permissions</TableHead>
              <TableHead className='w-[100px]'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className='text-center'>
                  Loading...
                </TableCell>
              </TableRow>
            ) : apiKeys.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className='text-center text-muted-foreground'
                >
                  No API keys found. Create one to get started.
                </TableCell>
              </TableRow>
            ) : (
              apiKeys.map(key => (
                <TableRow key={key.id}>
                  <TableCell className='font-medium'>{key.name}</TableCell>
                  <TableCell>
                    <div className='flex items-center gap-2'>
                      <span className='font-mono text-sm'>
                        {key.key.substring(0, 8)}...
                      </span>
                      <Button
                        variant='ghost'
                        size='icon'
                        className='h-8 w-8'
                        onClick={() => handleCopyKey(key.key)}
                      >
                        <Copy className='h-4 w-4' />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    {key.lastUsed
                      ? new Date(key.lastUsed).toLocaleDateString()
                      : 'Never'}
                  </TableCell>
                  <TableCell>
                    {new Date(key.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <span className='capitalize'>{key.permissions}</span>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant='ghost'
                      size='icon'
                      className='h-8 w-8 text-destructive'
                      onClick={() => handleDeleteKey(key.id)}
                      disabled={deleteKeyMutation.isPending}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
