'use client'

import React from 'react'

export function AccountSettings() {
  const handleSignOut = () => {
    // Client-side navigation or API call for sign-out
    window.location.href = '/sign-out' // Or preferably use Next.js router or an API call
  }

  return (
    <div className='bg-card rounded-lg p-6'>
      <h2 className='text-xl font-semibold mb-4'>Account</h2>
      <p className='text-sm text-muted-foreground mb-4'>
        Manage your account settings and preferences
      </p>
      <button
        className='bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors'
        onClick={handleSignOut}
      >
        Sign Out
      </button>
    </div>
  )
}
