import { Suspense } from 'react'
import { Podcast as PodcastIcon } from 'lucide-react'
import { PodcastSearchSkeleton } from '@/app/(dashboard)/_components/video-form'
import { PodcastWorkflow } from './_components/podcast-workflow'

// Static generation for fast initial load
export const revalidate = 3600 // 1 hour
export const dynamic = 'force-static'

export default function PodcastToVideoPage() {
  return (
    <div className='min-h-screen bg-background'>
      {/* Header Section - Static */}
      <div className='text-center space-y-2 py-8'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-pink-500/10 text-pink-600 dark:text-pink-400 rounded-full text-sm font-medium'>
          <PodcastIcon className='h-3 w-3' />
          Podcast to Video Generator
        </div>
        {/* <h1 className='text-2xl font-bold'>Podcast to Video</h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Transform podcast episodes into engaging videos with subtitles and
          visuals
        </p>
      </div>

      {/* Progressive Loading Workflow */}
      <Suspense fallback={<PodcastPageSkeleton />}>
        <PodcastWorkflow />
      </Suspense>
    </div>
  )
}

function PodcastPageSkeleton() {
  return (
    <div className='max-w-2xl mx-auto px-4 py-4'>
      <PodcastSearchSkeleton />
    </div>
  )
}
