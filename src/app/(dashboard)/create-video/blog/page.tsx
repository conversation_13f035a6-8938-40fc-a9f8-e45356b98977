import { Link, Globe } from 'lucide-react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import type { Metadata } from 'next'
import { BlogToVideoForm } from './_components'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 3600
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'Blog to Video - AI Blog Content Converter | Adori AI',
  description:
    'Transform your blog posts into engaging videos with AI. Simply paste your blog URL and let our AI analyze the content to create compelling video scripts with matching visuals and voiceovers.',
  keywords: [
    'blog to video converter',
    'AI content transformation',
    'blog post videos',
    'content repurposing',
    'video marketing',
    'blog content automation',
  ],
  openGraph: {
    title: 'Blog to Video - AI Blog Content Converter | Adori AI',
    description: 'Transform your blog posts into engaging videos with AI.',
    type: 'website',
  },
}

export default function BlogToVideoPage() {
  return (
    <div className='max-w-2xl mx-auto px-4 py-4 space-y-4'>
      {/* Header Section - Static, renders immediately */}
      <div className='text-center space-y-2'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-purple-500/10  text-purple-600 dark:text-purple-400 rounded-full text-sm font-medium'>
          <Link className='h-3 w-3' />
          AI Blog to Video Converter
        </div>
        {/* <h1 className='text-2xl font-bold'>
          Transform Blog Posts Into Engaging Videos
        </h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Simply paste your blog URL and let our AI analyze the content to
          create compelling video scripts with matching visuals and voiceovers.
        </p>
      </div>

      {/* Blog Configuration Card - Static structure */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <Globe className='h-4 w-4 text-purple-600 dark:text-purple-400' />
            Blog Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Client component with internal progressive loading */}
          <BlogToVideoForm />
        </CardContent>
      </Card>
    </div>
  )
}
