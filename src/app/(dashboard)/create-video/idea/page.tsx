import { <PERSON><PERSON><PERSON>, Lightbulb } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { Metadata } from 'next'
import { IdeaToVideoForm } from './_components'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 3600
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'Idea to Video - AI Video Script Generator | Adori AI',
  description:
    'Transform your ideas into engaging video scripts with AI. Generate scripts for social media videos, explainers, marketing campaigns, and more.',
  keywords: [
    'AI video script generator',
    'idea to video',
    'video script creation',
    'AI content creation',
    'social media videos',
    'marketing videos',
  ],
  openGraph: {
    title: 'Idea to Video - AI Video Script Generator | Adori AI',
    description: 'Transform your ideas into engaging video scripts with AI.',
    type: 'website',
  },
}

export default function IdeaToVideoPage() {
  return (
    <div className='max-w-2xl mx-auto px-4 py-4 space-y-4'>
      {/* Header Section - Static, renders immediately */}
      <div className='text-center space-y-2'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-blue-500/10 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium'>
          <Sparkles className='h-3 w-3' />
          AI Video Script Generator
        </div>
        {/* <h1 className='text-2xl font-bold'>
          Transform Your Ideas Into Video Scripts
        </h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Generate engaging scripts for social media videos, explainers,
          marketing campaigns etc.
        </p>
      </div>

      {/* Video Configuration Card - Static structure */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <Lightbulb className='h-4 w-4 text-blue-600 dark:text-blue-400' />
            Video Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Client component with internal progressive loading */}
          <IdeaToVideoForm />
        </CardContent>
      </Card>
    </div>
  )
}
