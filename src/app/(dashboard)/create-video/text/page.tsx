import { FileText, Type } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { Metadata } from 'next'
import { TextToVideoForm } from './_components/text-to-video-form'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 3600
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'Text to Video - AI Text Content Converter | Adori AI',
  description:
    'Transform your text content into engaging videos with AI. Convert articles, scripts, and written content into dynamic video presentations with AI-generated scenes and voiceovers.',
  keywords: [
    'text to video converter',
    'AI content transformation',
    'text content videos',
    'article to video',
    'script to video',
    'video creation',
    'content repurposing',
  ],
  openGraph: {
    title: 'Text to Video - AI Text Content Converter | Adori AI',
    description: 'Transform your text content into engaging videos with AI.',
    type: 'website',
  },
}

export default function TextToVideoPage() {
  return (
    <div className='max-w-6xl mx-auto px-4 py-4 space-y-4'>
      {/* Header Section - Static, renders immediately */}
      <div className='text-center space-y-2'>
        <div className='inline-flex items-center gap-2 px-3 py-1 bg-emerald-500/10 text-emerald-600 dark:text-emerald-400 rounded-full text-sm font-medium'>
          <Type className='h-3 w-3' />
          Text to Video Converter
        </div>
        {/* <h1 className='text-2xl font-bold'>
          Transform Your Text Into Engaging Videos
        </h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Convert your written content into professional videos with
          AI-generated scenes and voiceovers.
        </p>
      </div>

      {/* Text Configuration Card - Static structure */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <FileText className='h-4 w-4 text-emerald-600 dark:text-emerald-400' />
            Text Content Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Client component with internal progressive loading */}
          <TextToVideoForm />
        </CardContent>
      </Card>
    </div>
  )
}
