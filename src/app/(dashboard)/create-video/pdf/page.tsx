import { FileText, Upload } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import type { Metadata } from 'next'
import { PDFToVideoForm } from './_components/pdf-to-video-form'

// Enable static generation with ISR - revalidate every hour
export const revalidate = 3600
export const dynamic = 'force-static'

// Generate metadata for SEO optimization
export const metadata: Metadata = {
  title: 'PDF to Video - AI Video Generator | Adori AI',
  description:
    'Transform your PDF documents into engaging videos with AI. Convert presentations, reports, and documents into dynamic video content.',
  keywords: [
    'PDF to video',
    'document to video',
    'presentation to video',
    'AI video generator',
    'PDF converter',
    'video creation',
  ],
  openGraph: {
    title: 'PDF to Video - AI Video Generator | Adori AI',
    description: 'Transform your PDF documents into engaging videos with AI.',
    type: 'website',
  },
}

export default function PdfToVideoPage() {
  return (
    <div className='max-w-6xl mx-auto px-4 py-4 space-y-4'>
      {/* Header Section - Static, renders immediately */}
      <div className='text-center space-y-2'>
        <div className='inline-flex items-center gap-2 px-3 bg-orange-500/10 text-orange-600 dark:text-orange-400  rounded-full text-sm font-medium'>
          <Upload className='h-3 w-3' />
          PDF to Video Converter
        </div>
        {/* <h1 className='text-2xl font-bold'>
          Transform Your PDFs Into Engaging Videos
        </h1> */}
        <p className='text-muted-foreground text-sm max-w-2xl mx-auto'>
          Upload your PDF documents and convert them into dynamic video content
          with AI-powered narration
        </p>
      </div>

      {/* Video Configuration Card - Static structure */}
      <Card>
        <CardHeader className='pb-3'>
          <CardTitle className='flex items-center gap-2 text-lg'>
            <FileText className='h-4 w-4 text-orange-600 dark:text-orange-400' />
            PDF Upload & Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Client component with internal progressive loading */}
          <PDFToVideoForm />
        </CardContent>
      </Card>
    </div>
  )
}
