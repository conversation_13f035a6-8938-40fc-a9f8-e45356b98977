'use client'

import { useState, FormEvent, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Label } from '@/components/ui/label'
import { LoaderDialog } from '@/components/ui/loader-dialog'
import { Button } from '@/components/ui/button'
import { Upload, X, FileText } from 'lucide-react'
import { toast } from '@/lib/toast'
import { useVideoStore } from '@/store/video-store'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
import { useUser } from '@clerk/nextjs'
import { useInngestRunStatus } from '@/lib/useInngestRunStatus'
import { useQueryClient } from '@tanstack/react-query'
import { cn } from '@/lib/utils'
import { supabase } from '@/lib/supabase-client'

// Import reusable components
import {
  ToneSelector,
  AudienceSelector,
  PlatformSelector,
  IncludeOptions,
  KeywordsInput,
  AdvancedSettings,
  ActionButton,
  SideBySideSkeleton,
  type PDFVideoConfig,
} from '@/app/(dashboard)/_components/video-form'

// File interface for upload state
interface FileWithPreview {
  id: string
  name: string
  size: number
  type: string
  lastModified: number
  progress: number
  status: 'uploading' | 'uploaded' | 'error'
  file: File
  url: string
}

// Use the reusable configuration type
type VideoConfig = PDFVideoConfig

export function PDFToVideoForm() {
  const { user } = useUser()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [isDragActive, setIsDragActive] = useState(false)

  // Loading state tracking
  const [progressMessage, setProgressMessage] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)

  const [config, setConfig] = useState<VideoConfig>({
    tone: 'friendly',
    audience: 'general',
    platform: 'youtube',
    duration: 60,
    includeHook: true,
    includeCTA: true,
    language: 'english',
    orientation: 'landscape',
    autopick: 'mix',
    voice: null,
    keywords: '',
    pdfUrl: '',
    files: [],
  })

  const [eventId, setEventId] = useState<string | null>(null)
  const { status, output } = useInngestRunStatus(eventId)

  const updateConfig = (
    key: keyof VideoConfig,
    value: VideoConfig[keyof VideoConfig]
  ) => {
    setConfig(prev => ({ ...prev, [key]: value }))
  }

  const handleVoiceSelect = (voice: ElevenVoice) => {
    updateConfig('voice', voice)
    toast.success(`Voice "${voice.name}" selected`)
  }

  const simulateProgress = () => {
    setProgressMessage('📄 Processing PDF content and extracting text...')

    setTimeout(() => {
      setProgressMessage('🤖 Converting PDF content to video script...')
    }, 2000)

    setTimeout(() => {
      setProgressMessage('🎬 Finding perfect visual assets for your content...')
    }, 4000)

    setTimeout(() => {
      setProgressMessage('🎙️ Converting script to professional voiceovers...')
    }, 6000)

    setTimeout(() => {
      setProgressMessage('✨ Finalizing scenes and preparing your video...')
    }, 8000)

    setTimeout(() => {
      setProgressMessage('🎉 Almost done! Wrapping everything up...')
    }, 10000)
  }

  // File upload handling
  const handleFileSelect = useCallback(
    async (fileList: FileList) => {
      // Only allow single PDF upload
      if (files.length > 0) {
        toast.error('Please remove the current PDF before uploading a new one')
        return
      }

      const file = fileList[0] // Take only the first file
      if (!file) return

      if (file.type !== 'application/pdf') {
        toast.error('Please upload only PDF files')
        return
      }

      if (file.size > 50 * 1024 * 1024) {
        toast.error('File size must be less than 50MB')
        return
      }

      const fileWithPreview: FileWithPreview = {
        id: Math.random().toString(36).substring(2, 11),
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        progress: 0,
        status: 'uploading',
        file,
        url: '',
      }

      setFiles([fileWithPreview])

      try {
        // Simulate upload progress with faster intervals for better UX
        const progressInterval = setInterval(() => {
          setFiles(prev =>
            prev.map(f =>
              f.id === fileWithPreview.id
                ? { ...f, progress: Math.min(f.progress + 15, 90) }
                : f
            )
          )
        }, 150) // Faster progress updates

        // Upload directly to Supabase using frontend client for optimal performance
        const timestamp = Date.now()
        const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
        const filePath = `${user?.id}/pdfs/${timestamp}-${sanitizedFileName}`

        const { error: uploadError } = await supabase.storage
          .from('pdfs') // Use dedicated pdfs bucket
          .upload(filePath, file, {
            upsert: false,
            contentType: file.type,
          })

        clearInterval(progressInterval)

        if (uploadError) {
          console.error('Supabase upload error:', uploadError)
          throw new Error(`Upload failed: ${uploadError.message}`)
        }

        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('pdfs')
          .getPublicUrl(filePath)

        const publicUrl = urlData.publicUrl

        setFiles([
          {
            ...fileWithPreview,
            progress: 100,
            status: 'uploaded',
            url: publicUrl,
          },
        ])

        updateConfig('pdfUrl', publicUrl)
        toast.success('PDF uploaded successfully!')
      } catch (error) {
        setFiles([
          {
            ...fileWithPreview,
            progress: 100,
            status: 'error',
          },
        ])
        toast.error(
          `Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      }
    },
    [files.length, user?.id]
  )

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsDragActive(false)
      handleFileSelect(e.dataTransfer.files)
    },
    [handleFileSelect]
  )

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragActive(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragActive(false)
  }, [])

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
    updateConfig('pdfUrl', '')
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleGenerateScript = async (e?: FormEvent) => {
    e?.preventDefault()

    if (files.length === 0) {
      toast.error('Please upload at least one PDF file')
      return
    }

    if (!config.voice) {
      toast.error('Please select a voice for the video')
      return
    }

    if (!config.tone || !config.audience || !config.platform) {
      toast.error('Please select tone, audience, and platform')
      return
    }

    setIsGenerating(true)
    simulateProgress()

    const userId = user?.id
    try {
      // Use the first uploaded PDF file
      const selectedFile = files[0]

      // Prepare automation request with PDF URL
      const automationRequest = {
        idea: '',
        pdfUrl: selectedFile.url, // Pass the PDF URL instead of blogUrl
        tone: config.tone,
        audience: config.audience,
        platform: config.platform,
        hook: config.includeHook,
        callToAction: config.includeCTA,
        keywords: config.keywords || undefined,
        duration: config.duration,
        language: config.language,
        orientation: config.orientation,
        autopick: config.autopick,
        voice: config.voice,
        userId: userId,
        method: 'PDF to Video',
      }

      // Call the inngest API route
      const response = await fetch('/api/generate-video-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(automationRequest),
      })

      if (!response.ok) throw new Error('Failed to generate video')

      const result = await response.json()
      if (result.eventId) {
        setEventId(result.eventId)
      }
      // UI will update based on polling
    } catch (error) {
      console.error('Video generation error:', error)
      setProgressMessage('')
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to generate video. Please try again.'
      )
      setIsGenerating(false)
    }
    // Note: isGenerating stays true during polling, only set to false on completion or error
  }

  // Watch for polling completion
  useEffect(() => {
    if (
      status === 'Completed' &&
      output &&
      typeof output === 'object' &&
      output !== null &&
      'projectId' in output
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const project = output as any // API response will be transformed by setProjectData
      setProgressMessage('🎉 Video generation complete! Redirecting...')
      useVideoStore.getState().setProjectData(project)
      // Invalidate projects cache when a new project is created via Inngest
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Video created successfully!')
      setTimeout(() => {
        router.push(`/scene-editor?projectId=${project.projectId}`)
      }, 1500)
      setIsGenerating(false)
    } else if (status === 'Failed' || status === 'Cancelled') {
      setProgressMessage('')
      toast.error('Video generation failed. Please try again.')
      setIsGenerating(false)
    }
  }, [status, output, queryClient, router])

  // Simulate initial loading for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 100) // Just enough time to prevent layout shift

    return () => clearTimeout(timer)
  }, [])

  // Show skeleton during initial loading
  if (isInitialLoading) {
    return <SideBySideSkeleton leftSideType='pdf' />
  }

  // Button should only be enabled when PDF is successfully uploaded AND voice is selected
  const canGenerate =
    files.length > 0 &&
    files[0].status === 'uploaded' &&
    config.voice !== null &&
    !isGenerating

  return (
    <>
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Left Column - PDF Upload */}
        <div className='space-y-3'>
          <Label className='text-sm font-medium'>
            Upload your PDF documents
          </Label>

          {/* PDF Upload Area */}
          <div
            className={cn(
              'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
              isDragActive
                ? 'border-primary bg-primary/5'
                : 'border-muted-foreground/25 hover:border-muted-foreground/50',
              files.length > 0 && 'border-green-500/50 bg-green-500/5'
            )}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {files.length === 0 ? (
              <div className='space-y-4'>
                <div className='mx-auto w-12 h-12 bg-muted rounded-lg flex items-center justify-center'>
                  <Upload className='w-6 h-6 text-muted-foreground' />
                </div>
                <div>
                  <h3 className='text-sm font-medium'>Upload PDF files</h3>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Drag & drop PDF files here, or{' '}
                    <Button
                      variant='link'
                      className='p-0 h-auto text-xs'
                      onClick={() =>
                        document.getElementById('pdf-upload')?.click()
                      }
                    >
                      browse
                    </Button>
                  </p>
                  <p className='text-xs text-muted-foreground mt-2'>
                    Supports PDF files up to 50MB
                  </p>
                </div>
                <input
                  id='pdf-upload'
                  type='file'
                  accept='.pdf'
                  className='hidden'
                  onChange={e =>
                    e.target.files && handleFileSelect(e.target.files)
                  }
                />
              </div>
            ) : (
              <div className='space-y-3'>
                {files.map(file => (
                  <div
                    key={file.id}
                    className='flex items-center gap-3 p-3 bg-background rounded-lg border'
                  >
                    <FileText className='w-8 h-8 text-red-500 flex-shrink-0' />
                    <div className='flex-1 min-w-0'>
                      <p className='text-sm font-medium truncate'>
                        {file.name}
                      </p>
                      <p className='text-xs text-muted-foreground'>
                        {formatFileSize(file.size)}
                      </p>
                      {file.status === 'uploading' && (
                        <div className='w-full bg-muted rounded-full h-1.5 mt-1'>
                          <div
                            className='bg-primary h-1.5 rounded-full transition-all duration-300'
                            style={{ width: `${file.progress}%` }}
                          />
                        </div>
                      )}
                    </div>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => removeFile(file.id)}
                      className='flex-shrink-0'
                    >
                      <X className='w-4 h-4' />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Video Configuration */}
        <div className='space-y-4'>
          <Label className='text-sm font-medium'>Video Configuration</Label>

          {/* Tone, Audience, Platform Row */}
          <div className='flex gap-x-4 gap-y-2 flex-wrap'>
            <ToneSelector
              value={config.tone}
              onValueChange={value => updateConfig('tone', value)}
              disabled={isGenerating}
            />
            <AudienceSelector
              value={config.audience}
              onValueChange={value => updateConfig('audience', value)}
              disabled={isGenerating}
            />
            <PlatformSelector
              value={config.platform}
              onValueChange={value => updateConfig('platform', value)}
              disabled={isGenerating}
            />
          </div>

          {/* Include Options */}
          <IncludeOptions
            includeHook={config.includeHook}
            includeCTA={config.includeCTA}
            onIncludeHookChange={checked =>
              updateConfig('includeHook', checked)
            }
            onIncludeCTAChange={checked => updateConfig('includeCTA', checked)}
            disabled={isGenerating}
          />

          {/* Keywords */}
          <KeywordsInput
            value={config.keywords}
            onChange={value => updateConfig('keywords', value)}
            placeholder='e.g. technology, innovation, future'
            disabled={isGenerating}
          />

          {/* Advanced Settings */}
          <AdvancedSettings
            duration={config.duration}
            onDurationChange={value => updateConfig('duration', value)}
            voice={config.voice}
            onVoiceSelect={handleVoiceSelect}
            language={config.language}
            onLanguageChange={value => updateConfig('language', value)}
            orientation={config.orientation}
            onOrientationChange={value => updateConfig('orientation', value)}
            autopick={config.autopick}
            onAutopickChange={value => updateConfig('autopick', value)}
            disabled={isGenerating}
          />
        </div>
      </div>

      {/* Action Button */}
      <ActionButton
        onClick={handleGenerateScript}
        disabled={!canGenerate}
        isGenerating={isGenerating}
        progressMessage={progressMessage}
        actionText='Generate Video'
        loadingText='Creating Video...'
      />

      {/* Loader Dialog */}
      <LoaderDialog
        open={isGenerating || status === 'Running'}
        title='AI agent is cooking your video'
        subtitle={
          status === 'Running'
            ? 'Processing your request...'
            : progressMessage || 'This may take a few minutes...'
        }
      />
    </>
  )
}
