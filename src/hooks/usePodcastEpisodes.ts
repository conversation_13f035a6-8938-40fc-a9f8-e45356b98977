import { useQuery } from '@tanstack/react-query'

export type Episode = {
  id: number
  title: string
  description: string
  link: string
  guid: string
  datePublished: number
  datePublishedPretty: string
  enclosureUrl: string
  enclosureType: string
  enclosureLength: number
  duration: number
  durationFormatted: string
  explicit: boolean
  episode: number
  season: number
  image: string
  transcriptUrl?: string
  chaptersUrl?: string
}

export type EpisodesResponse = {
  status: string
  episodes: Episode[]
  count: number
}

export function usePodcastEpisodes(
  feedId: number | string,
  enabled: boolean = true,
  max: number = 50
) {
  return useQuery({
    queryKey: ['podcast-episodes', feedId, max],
    enabled: enabled && !!feedId,
    queryFn: async (): Promise<EpisodesResponse> => {
      const params = new URLSearchParams({
        feedId: feedId.toString(),
        max: max.toString(),
      })

      const res = await fetch(`/api/podcast/episodes?${params}`)
      if (!res.ok) {
        const error = await res.text()
        throw new Error(error || 'Failed to fetch episodes')
      }
      return res.json()
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: 2,
  })
}
