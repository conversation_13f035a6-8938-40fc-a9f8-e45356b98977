import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/lib/toast'

export interface Project {
  projectId: string
  projectName: string
  method: string
  updatedAt: string
  createdAt: string
  coverColor: string | null
  coverPic: string | null
  orientation: string
  duration: string | null
}

export interface ProjectsResponse {
  projects: Project[]
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

const fetchProjects = async (
  page = 1,
  limit = 8
): Promise<ProjectsResponse> => {
  const response = await fetch(`/api/projects?page=${page}&limit=${limit}`)

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || 'Failed to fetch projects')
  }

  return response.json()
}

const deleteProject = async (projectId: string): Promise<void> => {
  const response = await fetch('/api/projects', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ projectId }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || 'Failed to delete project')
  }
}

export function useProjects(page = 1, limit = 8) {
  return useQuery({
    queryKey: ['projects', page, limit],
    queryFn: () => fetchProjects(page, limit),
    // staleTime and gcTime now handled by global config
    refetchOnWindowFocus: true, // Refetch when user returns to tab
  })
}

export function useDeleteProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteProject,
    onSuccess: () => {
      // Invalidate and refetch projects
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Project deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete project: ${error.message}`)
    },
  })
}
