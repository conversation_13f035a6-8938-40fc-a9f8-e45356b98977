'use client'

import { useQueryClient } from '@tanstack/react-query'
import { useEffect, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import { createQueryPrefetcher, getQueryPrefetcher } from '@/lib/query-prefetch'

/**
 * Hook to initialize and manage query prefetching throughout the app
 */
export function useQueryPrefetch() {
  const queryClient = useQueryClient()
  const pathname = usePathname()

  // Skip prefetching on auth pages
  const isAuthPage =
    pathname.startsWith('/sign-in') || pathname.startsWith('/sign-up')

  // Initialize prefetcher on first render (skip for auth pages)
  useEffect(() => {
    if (isAuthPage) return

    const prefetcher = createQueryPrefetcher(queryClient)

    // Run background prefetching immediately
    prefetcher.backgroundPrefetch()

    console.log('🚀 Query prefetching initialized')
  }, [queryClient, isAuthPage])

  // Route-based prefetching when pathname changes (skip for auth pages)
  useEffect(() => {
    if (isAuthPage) return

    const prefetcher = getQueryPrefetcher()
    if (!prefetcher) return

    // Prefetch based on current route
    if (pathname === '/projects') {
      prefetcher.prefetchForRoute['/projects']()
    } else if (pathname.includes('/scene-editor')) {
      prefetcher.prefetchForRoute['/scene-editor']()
    } else if (pathname === '/create-video/podcast') {
      prefetcher.prefetchForRoute['/create-video/podcast']()
    }
  }, [pathname, isAuthPage])

  // Return prefetch functions for manual use in components
  const prefetch = useCallback(() => {
    const prefetcher = getQueryPrefetcher()
    if (!prefetcher) return {}

    return {
      // Hover prefetching
      onHoverProjects: prefetcher.prefetchOnHover.projects,
      onHoverMyVideos: prefetcher.prefetchOnHover.myVideos,
      onHoverCreateVideo: prefetcher.prefetchOnHover.createVideo,

      // Behavioral prefetching
      onSearchStart: prefetcher.prefetchBehavioral.onSearchStart,
      onProjectSelect: prefetcher.prefetchBehavioral.onProjectSelect,
      onMediaPickerOpen: prefetcher.prefetchBehavioral.onMediaPickerOpen,

      // Invalidation helpers
      invalidateOnProjectUpdate: prefetcher.invalidateRelated.onProjectUpdate,
      invalidateOnMediaUpload: prefetcher.invalidateRelated.onMediaUpload,
      invalidateOnProjectDelete: prefetcher.invalidateRelated.onProjectDelete,
    }
  }, [])

  return prefetch()
}
