import { useEffect, useState } from 'react'
import { useVideoStore } from '@/store/video-store'
import { toast } from '@/lib/toast'

interface UseProjectDataResult {
  isLoading: boolean
  error: string | null
  projectLoaded: boolean
}

export function useProjectData(projectId: string | null): UseProjectDataResult {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [projectLoaded, setProjectLoaded] = useState(false)
  const { setProjectData, project } = useVideoStore()

  useEffect(() => {
    // If no projectId or project already loaded for this ID, skip
    if (!projectId || (project && project.projectId === projectId)) {
      setProjectLoaded(!!project)
      return
    }

    const fetchProjectData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/projects/${projectId}`)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch project data')
        }

        const projectData = await response.json()

        // Set project data in store (transformation will happen automatically)
        setProjectData(projectData)
        setProjectLoaded(true)
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        toast.error(`Failed to load project: ${errorMessage}`)
        console.error('Error fetching project data:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProjectData()
  }, [projectId, project, setProjectData])

  return {
    isLoading,
    error,
    projectLoaded,
  }
}
