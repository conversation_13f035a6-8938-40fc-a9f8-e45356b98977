import { cache } from 'react'

// Browser storage keys
const STORAGE_KEYS = {
  PRICING_LOADED: 'clerk-pricing-loaded',
  PRICING_CACHE: 'clerk-pricing-cache',
  PRICING_TIMESTAMP: 'clerk-pricing-timestamp',
} as const

// Cache duration (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000

// Browser storage utilities
export const pricingStorage = {
  // Check if pricing is cached and valid
  isCached: (): boolean => {
    if (typeof window === 'undefined') return false

    try {
      const timestamp = sessionStorage.getItem(STORAGE_KEYS.PRICING_TIMESTAMP)
      const isLoaded = sessionStorage.getItem(STORAGE_KEYS.PRICING_LOADED)

      if (!timestamp || !isLoaded) return false

      const now = Date.now()
      const cacheTime = parseInt(timestamp, 10)

      return now - cacheTime < CACHE_DURATION
    } catch {
      return false
    }
  },

  // Mark pricing as loaded
  markLoaded: (): void => {
    if (typeof window === 'undefined') return

    try {
      const now = Date.now().toString()
      sessionStorage.setItem(STORAGE_KEYS.PRICING_LOADED, 'true')
      sessionStorage.setItem(STORAGE_KEYS.PRICING_TIMESTAMP, now)
    } catch {
      // Fail silently if storage is not available
    }
  },

  // Clear pricing cache
  clearCache: (): void => {
    if (typeof window === 'undefined') return

    try {
      sessionStorage.removeItem(STORAGE_KEYS.PRICING_LOADED)
      sessionStorage.removeItem(STORAGE_KEYS.PRICING_TIMESTAMP)
      sessionStorage.removeItem(STORAGE_KEYS.PRICING_CACHE)
    } catch {
      // Fail silently
    }
  },
}

// Cache pricing page data for better performance
export const getPricingPageData = cache(async () => {
  // This would typically fetch from your pricing API
  // For now, return static data that could be cached
  return {
    plans: [
      {
        name: 'Free Trial',
        description: 'Try Adori AI with basic features',
        features: ['1 video creation', 'HD resolution', '20 AI images'],
      },
      {
        name: 'Basic',
        description: 'Perfect for individual creators',
        features: ['10 videos/month', 'HD resolution', 'Unlimited AI images'],
      },
      {
        name: 'Premium',
        description: 'For professional content creators',
        features: ['50 videos/month', '4K resolution', 'Pro voices'],
      },
      {
        name: 'Business',
        description: 'For teams and businesses',
        features: ['Unlimited videos', '4K resolution', 'Priority support'],
      },
    ],
    lastUpdated: new Date().toISOString(),
  }
})

// Cache headers for pricing page
export const getPricingCacheHeaders = () => {
  return {
    'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
    'CDN-Cache-Control': 'public, s-maxage=3600',
    'Vercel-CDN-Cache-Control': 'public, s-maxage=3600',
  }
}

// Performance monitoring for pricing page
export const trackPricingPagePerformance = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    // Track page load time
    window.addEventListener('load', () => {
      const loadTime = performance.now()
      console.log(`Pricing page loaded in ${loadTime.toFixed(2)}ms`)

      // Track Core Web Vitals if available
      if ('web-vitals' in window) {
        // This would integrate with your analytics
        console.log('Core Web Vitals tracking enabled')
      }
    })
  }
}

// Preload critical resources for pricing page
export const preloadPricingResources = () => {
  if (typeof document !== 'undefined') {
    // Preload Clerk resources
    const clerkCSS = document.createElement('link')
    clerkCSS.rel = 'preload'
    clerkCSS.as = 'style'
    clerkCSS.href = 'https://js.clerk.dev/v1/clerk.css'
    document.head.appendChild(clerkCSS)

    // Preload fonts if needed
    const fontPreload = document.createElement('link')
    fontPreload.rel = 'preload'
    fontPreload.as = 'font'
    fontPreload.type = 'font/woff2'
    fontPreload.crossOrigin = 'anonymous'
    // Add your font URL here if you have custom fonts

    // Prefetch related pages
    const prefetchLinks = ['/sign-up', '/']
    prefetchLinks.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = href
      document.head.appendChild(link)
    })
  }
}
