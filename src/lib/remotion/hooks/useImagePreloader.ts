/**
 * useImagePreloader Hook
 * 
 * Preloads all images in scenes before Remotion starts rendering to prevent
 * white/blank frames from appearing while images are loading. This is especially
 * important for user-uploaded Supabase images that may have network latency.
 * 
 * Uses Remotion's delayRender/continueRender mechanism to ensure all images
 * are loaded before video rendering begins.
 */

import { useEffect } from 'react'
import { delayRender, continueRender } from 'remotion'
import type { Scene } from '../types'

export const useImagePreloader = (
  scenes: Scene[],
  isRenderingContext: boolean
) => {
  useEffect(() => {
    // Only run during video rendering, not preview
    if (!isRenderingContext) return

    // Extract all image URLs from scenes
    const imageUrls = scenes
      .map(scene => scene.media?.url)
      .filter((url, index) => {
        const scene = scenes[index]
        return url && scene.media?.type === 'image'
      }) as string[]

    // If no images to preload, return early
    if (imageUrls.length === 0) return

    console.log(`🖼️ Preloading ${imageUrls.length} images for rendering...`)

    // Use Remotion's delayRender to pause rendering until images are loaded
    const handle = delayRender('Preloading scene images')

    // Create preload promises for all images
    const preloadPromises = imageUrls.map(
      (url, index) =>
        new Promise<string>((resolve) => {
          const img = new Image()
          
          img.onload = () => {
            console.log(`✅ Image ${index + 1}/${imageUrls.length} loaded:`, url.substring(0, 50) + '...')
            resolve(url)
          }
          
          img.onerror = (error) => {
            console.warn(`⚠️ Image ${index + 1}/${imageUrls.length} failed to load:`, url.substring(0, 50) + '...', error)
            // Resolve even on error to prevent blocking the entire render
            resolve(url)
          }
          
          // Set crossOrigin for Supabase images to handle CORS
          img.crossOrigin = 'anonymous'
          img.src = url
        })
    )

    // Create timeout promise to prevent infinite waiting
    const timeoutPromise = new Promise<void>((resolve) => {
      setTimeout(() => {
        console.warn('⏰ Image preloading timeout reached (10s), continuing with render...')
        resolve()
      }, 10000) // 10 second timeout
    })

    // Wait for all images to load or timeout
    Promise.race([
      Promise.all(preloadPromises).then(() => {
        console.log('✅ All images preloaded successfully')
      }),
      timeoutPromise
    ])
      .then(() => {
        continueRender(handle)
      })
      .catch((error) => {
        console.error('❌ Error during image preloading:', error)
        // Continue rendering even if preloading fails
        continueRender(handle)
      })

    // Cleanup function
    return () => {
      try {
        continueRender(handle)
      } catch (error) {
        // Handle might already be resolved
        console.debug('Image preloader cleanup:', error)
      }
    }
  }, [scenes, isRenderingContext])
}
