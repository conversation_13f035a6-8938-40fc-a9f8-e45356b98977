import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'

export interface ScriptGenerationRequest {
  idea: string
  tone: string
  audience: string
  platform: string
  hook: boolean
  callToAction: boolean
  keywords?: string
  duration: number
  language: string
  orientation: string
  autopick: string
  blogUrl?: string
  pdfUrl?: string
  audioUrl?: string
}

export interface Caption {
  start: number
  end: number
  sentence: string
  wordBoundries: Array<{ start: number; end: number; word: string }>
}

export interface Speech {
  enabled: boolean
  src: string
  name: string
  volume: number
  transcript: {
    captions: Caption[]
    status: 'QUEUED' | 'COMPLETED' | 'FAILED'
  }
}

export interface AutomationRequest extends ScriptGenerationRequest {
  voice?: ElevenVoice | null
  userId?: string
  method?: string
  podcastUrl?: string
  episodeTitle?: string
  episodeDescription?: string
  episodeDuration?: number
  clipPace?: 'fast' | 'medium' | 'slow' | 'verySlow'
  audioName?: string
}
