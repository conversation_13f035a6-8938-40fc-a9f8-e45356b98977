'use client'

import { useState, useEffect } from 'react'

export function useInngestRunStatus(eventId: string | null) {
  const [status, setStatus] = useState<string | null>(null)
  const [output, setOutput] = useState<unknown>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  useEffect(() => {
    if (!eventId) return
    let cancelled = false
    setLoading(true)
    setStatus(null)
    setOutput(null)
    setError(null)
    setRetryCount(0)

    async function poll() {
      try {
        const res = await fetch(`/api/video-data-status?eventId=${eventId}`)

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}))

          // Handle "No runs found" error - this is expected initially
          if (
            res.status === 404 &&
            errorData.error === 'No runs found for this event'
          ) {
            if (cancelled) return

            // Retry with exponential backoff (max 10 retries)
            if (retryCount < 10) {
              const delay = Math.min(1000 * Math.pow(2, retryCount), 10000) // Max 10 seconds
              console.log(
                `🔄 Run not found yet, retrying in ${delay}ms (attempt ${retryCount + 1}/10)`
              )
              setRetryCount(prev => prev + 1)
              setTimeout(poll, delay)
              return
            } else {
              // Max retries reached
              setError('Function run not started after maximum retries')
              setLoading(false)
              return
            }
          }

          throw new Error(
            `Failed to fetch run status: ${errorData.error || res.statusText}`
          )
        }

        const runs = await res.json()
        if (!Array.isArray(runs) || runs.length === 0) {
          throw new Error('No runs found')
        }

        const run = runs[0]
        if (cancelled) return

        setStatus(run.status)

        if (run.status === 'Completed') {
          setOutput(run.output)
          setLoading(false)
        } else if (run.status === 'Failed' || run.status === 'Cancelled') {
          setError(`Run ${run.status}`)
          setLoading(false)
        } else {
          // Continue polling for running status
          setTimeout(poll, 1500)
        }
      } catch (err: unknown) {
        if (cancelled) return
        setError(err instanceof Error ? err.message : 'Unknown error')
        setLoading(false)
      }
    }

    poll()
    return () => {
      cancelled = true
    }
  }, [eventId])

  return { status, output, error, loading, retryCount }
}
