export interface UploadResult {
  success: boolean
  url: string
  path: string
  filename: string
  size: number
  type: string
  bucket: string
}

export interface UploadError {
  error: string
}

export async function uploadFile(file: File): Promise<UploadResult> {
  const formData = new FormData()
  formData.append('file', file)

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    const errorData: UploadError = await response.json()
    throw new Error(errorData.error || 'Upload failed')
  }

  return await response.json()
}
