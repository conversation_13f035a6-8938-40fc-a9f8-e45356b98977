import { toast as sonnerToast } from 'sonner'

// Success toast
export const success = (
  message: string,
  options?: Parameters<typeof sonnerToast.success>[1]
) => {
  return sonnerToast.success(message, {
    duration: 4000,
    ...options,
  })
}

// Error toast
export const error = (
  message: string,
  options?: Parameters<typeof sonnerToast.error>[1]
) => {
  return sonnerToast.error(message, {
    duration: 5000,
    ...options,
  })
}

// Warning toast
export const warning = (
  message: string,
  options?: Parameters<typeof sonnerToast.warning>[1]
) => {
  return sonnerToast.warning(message, {
    duration: 4000,
    ...options,
  })
}

// Info toast
export const info = (
  message: string,
  options?: Parameters<typeof sonnerToast.info>[1]
) => {
  return sonnerToast.info(message, {
    duration: 4000,
    ...options,
  })
}

// Loading toast
export const loading = (
  message: string,
  options?: Parameters<typeof sonnerToast.loading>[1]
) => {
  return sonnerToast.loading(message, {
    duration: Infinity,
    ...options,
  })
}

// Promise toast for async operations
export const promise = <T>(
  promise: Promise<T>,
  messages: {
    loading: string
    success: string | ((data: T) => string)
    error: string | ((error: unknown) => string)
  }
) => {
  return sonnerToast.promise(promise, messages)
}

// Dismiss a specific toast
export const dismiss = (toastId?: string | number) => {
  return sonnerToast.dismiss(toastId)
}

// Dismiss all toasts
export const dismissAll = () => {
  return sonnerToast.dismiss()
}

// API Error handler - converts API errors to user-friendly messages
export const apiError = (
  error: unknown,
  fallbackMessage = 'Something went wrong'
) => {
  let message = fallbackMessage

  if (error && typeof error === 'object' && 'response' in error) {
    const apiError = error as { response: { data: { message: string } } }
    if (apiError.response?.data?.message) {
      message = apiError.response.data.message
    }
  } else if (error && typeof error === 'object' && 'message' in error) {
    const simpleError = error as { message: string }
    message = simpleError.message
  } else if (typeof error === 'string') {
    message = error
  }

  return sonnerToast.error(message, {
    duration: 5000,
  })
}

// Render progress toast for video rendering
export const renderProgress = (progress: string, toastId?: string | number) => {
  if (toastId) {
    return sonnerToast.loading(progress, { id: toastId })
  }
  return sonnerToast.loading(progress, { duration: Infinity })
}

// Caption template success
export const captionTemplateSuccess = (templateName: string) => {
  return success(`Caption template "${templateName}" created successfully!`)
}

// Custom toast with JSX content
export const custom = (
  content: React.ReactNode,
  options?: Parameters<typeof sonnerToast>[1]
) => {
  return sonnerToast(content, options)
}

// Export the main toast object with all methods
export const toast = {
  success,
  error,
  warning,
  info,
  loading,
  promise,
  custom,
  dismiss,
  dismissAll,
  apiError,
  renderProgress,
  captionTemplateSuccess,
}
