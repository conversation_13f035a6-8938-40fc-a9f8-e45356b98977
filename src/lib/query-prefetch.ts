import { QueryClient } from '@tanstack/react-query'

/**
 * Prefetch strategies for different routes and user interactions
 */
export class QueryPrefetcher {
  constructor(private queryClient: QueryClient) {}

  /**
   * Prefetch data when user hovers over navigation links
   */
  prefetchOnHover = {
    projects: () => {
      this.queryClient.prefetchQuery({
        queryKey: ['projects', 1, 8],
        queryFn: () =>
          fetch('/api/projects?page=1&limit=8').then(res => res.json()),
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    },

    myVideos: () => {
      // Prefetch first page of videos
      this.queryClient.prefetchQuery({
        queryKey: ['media-assets', { page: 1, limit: 20, type: 'video' }],
        queryFn: () =>
          fetch('/api/media/upload?page=1&limit=20&type=video').then(res =>
            res.json()
          ),
        staleTime: 10 * 60 * 1000, // 10 minutes
      })
    },

    createVideo: () => {
      // Prefetch fonts for video creation
      this.queryClient.prefetchQuery({
        queryKey: ['fonts'],
        queryFn: () => fetch('/api/fonts').then(res => res.json()),
        staleTime: 24 * 60 * 60 * 1000, // 24 hours
      })

      // Prefetch voices
      this.queryClient.prefetchQuery({
        queryKey: ['voices'],
        queryFn: () => fetch('/api/elevenlabs/voices').then(res => res.json()),
        staleTime: 60 * 60 * 1000, // 1 hour
      })
    },
  }

  /**
   * Prefetch data based on current route
   */
  prefetchForRoute = {
    '/projects': () => {
      // Prefetch next page of projects
      this.queryClient.prefetchQuery({
        queryKey: ['projects', 2, 8],
        queryFn: () =>
          fetch('/api/projects?page=2&limit=8').then(res => res.json()),
        staleTime: 5 * 60 * 1000,
      })
    },

    '/scene-editor': () => {
      // Prefetch music library
      this.queryClient.prefetchQuery({
        queryKey: ['music', { page: 1, limit: 20 }],
        queryFn: () =>
          fetch('/api/music?page=1&limit=20').then(res => res.json()),
        staleTime: 30 * 60 * 1000,
      })

      // Prefetch media assets
      this.queryClient.prefetchQuery({
        queryKey: ['media-assets', { page: 1, limit: 20 }],
        queryFn: () =>
          fetch('/api/media/upload?page=1&limit=20').then(res => res.json()),
        staleTime: 10 * 60 * 1000,
      })
    },

    '/create-video/podcast': () => {
      // Prefetch popular podcasts (empty search to get trending)
      this.queryClient.prefetchQuery({
        queryKey: ['podcast-search', 'trending', 20],
        queryFn: () =>
          fetch('/api/podcast/search?q=trending&max=20').then(res =>
            res.json()
          ),
        staleTime: 30 * 60 * 1000,
      })
    },
  }

  /**
   * Prefetch data based on user behavior patterns
   */
  prefetchBehavioral = {
    // When user starts typing in search, prefetch common results
    onSearchStart: (searchType: 'podcast' | 'music' | 'media') => {
      switch (searchType) {
        case 'podcast':
          // Prefetch popular podcast categories
          const popularPodcastQueries = [
            'technology',
            'business',
            'education',
            'news',
          ]
          popularPodcastQueries.forEach(query => {
            this.queryClient.prefetchQuery({
              queryKey: ['podcast-search', query, 20],
              queryFn: () =>
                fetch(`/api/podcast/search?q=${query}&max=20`).then(res =>
                  res.json()
                ),
              staleTime: 30 * 60 * 1000,
            })
          })
          break

        case 'music':
          // Prefetch popular music genres
          const popularGenres = ['ambient', 'corporate', 'upbeat', 'cinematic']
          popularGenres.forEach(genre => {
            this.queryClient.prefetchQuery({
              queryKey: ['music', { page: 1, limit: 20, genre }],
              queryFn: () =>
                fetch(`/api/music?page=1&limit=20&genre=${genre}`).then(res =>
                  res.json()
                ),
              staleTime: 30 * 60 * 1000,
            })
          })
          break

        case 'media':
          // Prefetch popular media search terms
          const popularMediaQueries = [
            'business',
            'technology',
            'nature',
            'people',
          ]
          popularMediaQueries.forEach(query => {
            this.queryClient.prefetchQuery({
              queryKey: ['pexels-images', query, 'landscape', 1],
              queryFn: () =>
                fetch(
                  `/api/media/pexels/images?query=${query}&orientation=landscape&page=1`
                ).then(res => res.json()),
              staleTime: 15 * 60 * 1000,
            })
          })
          break
      }
    },

    // When user selects a project, prefetch related data
    onProjectSelect: (projectId: string) => {
      // Prefetch project details
      this.queryClient.prefetchQuery({
        queryKey: ['project', projectId],
        queryFn: () =>
          fetch(`/api/projects/${projectId}`).then(res => res.json()),
        staleTime: 5 * 60 * 1000,
      })
    },

    // When user opens media picker, prefetch next page
    onMediaPickerOpen: (currentPage: number = 1) => {
      this.queryClient.prefetchQuery({
        queryKey: ['media-assets', { page: currentPage + 1, limit: 20 }],
        queryFn: () =>
          fetch(`/api/media/upload?page=${currentPage + 1}&limit=20`).then(
            res => res.json()
          ),
        staleTime: 10 * 60 * 1000,
      })
    },
  }

  /**
   * Background prefetch for commonly accessed data
   */
  backgroundPrefetch = () => {
    // Prefetch fonts in background (they're used everywhere)
    this.queryClient.prefetchQuery({
      queryKey: ['fonts'],
      queryFn: () => fetch('/api/fonts').then(res => res.json()),
      staleTime: 24 * 60 * 60 * 1000,
    })

    // Prefetch voices in background - match the query key used by useElevenVoicesQuery
    this.queryClient.prefetchQuery({
      queryKey: ['eleven-voices'],
      queryFn: async () => {
        const response = await fetch('/api/elevenlabs/voices')
        if (!response.ok) {
          const error = await response.text()
          throw new Error(`Failed to fetch voices: ${error}`)
        }
        const data = await response.json()
        return data.voices // Return just the voices array to match the hook
      },
      staleTime: 60 * 60 * 1000, // 1 hour
    })

    // Prefetch user's recent projects
    this.queryClient.prefetchQuery({
      queryKey: ['projects', 1, 8],
      queryFn: () =>
        fetch('/api/projects?page=1&limit=8').then(res => res.json()),
      staleTime: 5 * 60 * 1000,
    })
  }

  /**
   * Invalidate related queries when data changes
   */
  invalidateRelated = {
    onProjectUpdate: (projectId: string) => {
      this.queryClient.invalidateQueries({ queryKey: ['projects'] })
      this.queryClient.invalidateQueries({ queryKey: ['project', projectId] })
    },

    onMediaUpload: () => {
      this.queryClient.invalidateQueries({ queryKey: ['media-assets'] })
    },

    onProjectDelete: () => {
      this.queryClient.invalidateQueries({ queryKey: ['projects'] })
    },
  }
}

/**
 * Create a singleton instance for the app
 */
let prefetcher: QueryPrefetcher | null = null

export function createQueryPrefetcher(
  queryClient: QueryClient
): QueryPrefetcher {
  if (!prefetcher) {
    prefetcher = new QueryPrefetcher(queryClient)
  }
  return prefetcher
}

export function getQueryPrefetcher(): QueryPrefetcher | null {
  return prefetcher
}
