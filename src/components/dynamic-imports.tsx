import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui/skeleton'
import { SceneEditorSkeleton } from '@/app/(scene-editor)/_components/skeleton-loaders'

// Loading fallback for scene editor
const SceneEditorLoading = () => <SceneEditorSkeleton />

// Loading fallback for video creation forms
const VideoFormLoading = () => (
  <div className='max-w-2xl mx-auto px-4 py-4 space-y-4'>
    <div className='text-center space-y-2'>
      <Skeleton className='h-8 w-48 mx-auto rounded-full' />
      <Skeleton className='h-7 w-80 mx-auto' />
      <Skeleton className='h-5 w-96 mx-auto' />
    </div>
    <Skeleton className='h-96 w-full rounded-lg' />
    <Skeleton className='h-10 w-full rounded-md' />
  </div>
)

// Loading fallback for media pickers
const MediaPickerLoading = () => (
  <div className='space-y-4'>
    <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'>
      {Array.from({ length: 8 }).map((_, i) => (
        <Skeleton key={i} className='aspect-video w-full rounded-lg' />
      ))}
    </div>
  </div>
)

// Dynamically imported components with loading states
export const DynamicSceneEditor = dynamic(
  () => import('@/app/(scene-editor)/scene-editor/page'),
  {
    loading: SceneEditorLoading,
    ssr: false, // Scene editor is client-heavy
  }
)

export const DynamicMediaPickerModal = dynamic(
  () => import('@/app/(scene-editor)/_components/media-picker-modal').then(mod => ({ default: mod.MediaPickerModal })),
  {
    loading: MediaPickerLoading,
    ssr: false,
  }
)

export const DynamicMusicPickerModal = dynamic(
  () => import('@/app/(scene-editor)/_components/music-picker-modal').then(mod => ({ default: mod.MusicPickerModal })),
  {
    loading: MediaPickerLoading,
    ssr: false,
  }
)

export const DynamicVoicePickerModal = dynamic(
  () => import('@/app/(scene-editor)/_components/voice-picker-modal').then(mod => ({ default: mod.VoicePickerModal })),
  {
    loading: () => (
      <div className='space-y-4'>
        <Skeleton className='h-10 w-full' />
        <div className='grid grid-cols-1 gap-3'>
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className='flex items-center gap-3 p-3 border rounded-lg'>
              <Skeleton className='h-10 w-10 rounded-full' />
              <div className='flex-1 space-y-2'>
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-3 w-48' />
              </div>
              <Skeleton className='h-8 w-8 rounded-full' />
            </div>
          ))}
        </div>
      </div>
    ),
    ssr: false,
  }
)

// Dynamic imports for video creation forms
export const DynamicIdeaToVideoForm = dynamic(
  () => import('@/app/(dashboard)/create-video/idea/page'),
  {
    loading: VideoFormLoading,
    ssr: true, // Forms can be SSR'd
  }
)

export const DynamicBlogToVideoForm = dynamic(
  () => import('@/app/(dashboard)/create-video/blog/page'),
  {
    loading: VideoFormLoading,
    ssr: true,
  }
)

export const DynamicTextToVideoForm = dynamic(
  () => import('@/app/(dashboard)/create-video/text/page'),
  {
    loading: VideoFormLoading,
    ssr: true,
  }
)

export const DynamicPdfToVideoForm = dynamic(
  () => import('@/app/(dashboard)/create-video/pdf/page'),
  {
    loading: VideoFormLoading,
    ssr: true,
  }
)

export const DynamicAudioToVideoForm = dynamic(
  () => import('@/app/(dashboard)/create-video/audio/page'),
  {
    loading: VideoFormLoading,
    ssr: true,
  }
)

export const DynamicPodcastToVideoForm = dynamic(
  () => import('@/app/(dashboard)/create-video/podcast/page'),
  {
    loading: VideoFormLoading,
    ssr: true,
  }
)

// Dynamic Remotion preview (heavy component)
export const DynamicRemotionPreview = dynamic(
  () => import('@/app/(scene-editor)/_components/dynamic-remotion-preview').then(mod => ({ default: mod.DynamicRemotionPreview })),
  {
    loading: () => (
      <div className='aspect-video bg-muted rounded-lg flex items-center justify-center'>
        <div className='text-center space-y-2'>
          <div className='w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto' />
          <p className='text-sm text-muted-foreground'>Loading video preview...</p>
        </div>
      </div>
    ),
    ssr: false,
  }
)
