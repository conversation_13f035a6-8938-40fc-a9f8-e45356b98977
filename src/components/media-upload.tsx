'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, Loader2 } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useMediaUpload } from '@/hooks/useMediaUpload'
import { formatFileSize } from '@/hooks/useMediaAssets'
import { toast } from '@/lib/toast'

interface MediaUploadProps {
  onUploadComplete?: (result: unknown) => void
  onUploadError?: (error: Error) => void
  acceptedTypes?: string[]
  maxFileSize?: number
  multiple?: boolean
  className?: string
}

export function MediaUpload({
  onUploadComplete,
  onUploadError,
  acceptedTypes = ['image/*', 'video/*'],
  maxFileSize = 500 * 1024 * 1024, // 500MB
  multiple = true,
  className = '',
}: MediaUploadProps) {
  const { uploadMultipleMedia, batchProgress } = useMediaUpload()
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (isUploading) {
        toast.error('Upload already in progress')
        return
      }

      setIsUploading(true)

      try {
        const results = await uploadMultipleMedia(acceptedFiles)

        // Call onUploadComplete for each successful upload
        results.forEach(result => {
          onUploadComplete?.(result)
        })

        toast.success(
          `Successfully uploaded ${results.length} file${results.length !== 1 ? 's' : ''}!`
        )
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Upload failed'
        toast.error(errorMessage)
        onUploadError?.(
          error instanceof Error ? error : new Error('Upload failed')
        )
      } finally {
        setIsUploading(false)
      }
    },
    [uploadMultipleMedia, onUploadComplete, onUploadError, isUploading]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce(
      (acc, type) => {
        acc[type] = []
        return acc
      },
      {} as Record<string, string[]>
    ),
    maxSize: maxFileSize,
    multiple,
    onDropRejected: rejectedFiles => {
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            toast.error(
              `File "${file.name}" is too large. Maximum size is ${formatFileSize(maxFileSize)}.`
            )
          } else if (error.code === 'file-invalid-type') {
            toast.error(`File "${file.name}" has an invalid type.`)
          } else {
            toast.error(`Error with file "${file.name}": ${error.message}`)
          }
        })
      })
    },
  })

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Compact Drop Zone - Inspired by PDF Dropper */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
          transition-all duration-300 ease-in-out group
          ${
            isDragActive
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground/25 hover:border-primary/60 hover:bg-muted/30'
          }
        `}
      >
        <input {...getInputProps()} />

        <div className='flex flex-col items-center justify-center'>
          {/* Upload Icon */}
          <div
            className={`
              transition-all duration-300 mb-3
              ${
                isDragActive
                  ? 'text-primary scale-110'
                  : 'text-muted-foreground group-hover:text-primary/70'
              }
            `}
          >
            <Upload className='h-10 w-10' />
          </div>

          {/* Text Content */}
          {isDragActive ? (
            <div className='space-y-1'>
              <p className='font-semibold text-primary'>
                Drop the files here...
              </p>
              <p className='text-sm text-primary/70'>Release to upload</p>
            </div>
          ) : (
            <div className='space-y-2'>
              <p className='font-semibold text-foreground'>
                Drop files here or click to browse
              </p>
              <p className='text-sm text-muted-foreground'>
                Supports images and videos up to {formatFileSize(maxFileSize)}
              </p>

              {/* Compact Format Tags */}
              <div className='flex flex-wrap justify-center gap-1 text-xs text-muted-foreground/70 mt-2'>
                {['JPEG', 'PNG', 'WebP', 'GIF', 'MP4', 'WebM'].map(format => (
                  <span
                    key={format}
                    className='px-1.5 py-0.5 bg-muted rounded text-xs'
                  >
                    {format}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Batch Upload Progress */}
      {batchProgress && (
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-sm font-semibold text-foreground'>
              Uploading Files
            </h3>
            <Badge variant='secondary' className='text-xs'>
              {batchProgress.completedFiles} of {batchProgress.totalFiles}{' '}
              complete
            </Badge>
          </div>

          {/* Overall Progress */}
          <div className='bg-card border rounded-lg p-4 shadow-sm'>
            <div className='space-y-3'>
              {/* Current File Info */}
              <div className='flex items-center space-x-3'>
                <Loader2 className='h-4 w-4 animate-spin text-primary flex-shrink-0' />
                <div className='flex-1 min-w-0'>
                  <p className='text-sm font-medium text-card-foreground truncate'>
                    {batchProgress.currentFile}
                  </p>
                  <p className='text-xs text-muted-foreground'>
                    Processing {batchProgress.completedFiles + 1} of{' '}
                    {batchProgress.totalFiles} files
                  </p>
                </div>
              </div>

              {/* Overall Progress Bar */}
              <div className='space-y-2'>
                <div className='flex items-center justify-between'>
                  <span className='text-xs text-muted-foreground'>
                    Overall Progress
                  </span>
                  <span className='text-xs font-semibold text-primary'>
                    {batchProgress.overallProgress}%
                  </span>
                </div>
                <Progress
                  value={batchProgress.overallProgress}
                  className='h-2'
                />
              </div>

              {/* Individual File Progress (if available) */}
              {Object.keys(batchProgress.fileProgresses).length > 0 && (
                <div className='space-y-2 max-h-32 overflow-y-auto'>
                  <p className='text-xs font-medium text-muted-foreground'>
                    File Details:
                  </p>
                  {Object.entries(batchProgress.fileProgresses).map(
                    ([fileId, progress]) => (
                      <div
                        key={fileId}
                        className='flex items-center justify-between text-xs'
                      >
                        <span className='text-muted-foreground truncate flex-1'>
                          {progress.message}
                        </span>
                        <span className='text-primary font-medium ml-2'>
                          {progress.progress}%
                        </span>
                      </div>
                    )
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
