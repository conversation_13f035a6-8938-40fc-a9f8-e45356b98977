'use client'

import * as React from 'react'
import Image from 'next/image'

import { NavMain } from './nav-main'
import { NavUser } from './nav-user'
import { ThemeSwitcher } from './theme-switcher'
import { UsageMetrics } from '@/components/usage-metrics'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { navMain } from '@/config/nav-config'
import { useTheme } from 'next-themes'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { theme } = useTheme()
  const [mounted, setMounted] = React.useState(false)
  React.useEffect(() => {
    setMounted(true)
  }, [])
  const logoSrc =
    theme === 'dark' ? '/adori-logo-dark.svg' : '/adori-logo-light.svg'

  // Use the sidebar hook to get collapsed state
  const { state } = useSidebar()
  const isCollapsed = state === 'collapsed'

  return (
    <Sidebar collapsible='icon' {...props}>
      <SidebarHeader className=''>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            {/* Logo for expanded sidebar */}
            <div className='group-data-[collapsible=icon]:hidden flex items-center'>
              {mounted && (
                <Image
                  src={logoSrc}
                  alt='Adori AI Logo'
                  width={32}
                  height={32}
                />
              )}
              <span className='ml-2 text-xl font-semibold'>Adori AI</span>
            </div>

            {/* Logo for collapsed sidebar */}
            <div className='hidden group-data-[collapsible=icon]:block'>
              {mounted && (
                <Image
                  src={logoSrc}
                  alt='Adori AI Logo'
                  width={32}
                  height={32}
                />
              )}
            </div>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMain} />
      </SidebarContent>
      <SidebarFooter className='flex flex-col gap-2'>
        {/* Usage metrics with new design */}
        <UsageMetrics isCollapsed={isCollapsed} />

        {/* Theme toggle in footer */}
        <SidebarMenu className='mb-2'>
          <SidebarMenuItem className=''>
            <ThemeSwitcher />
          </SidebarMenuItem>
        </SidebarMenu>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
