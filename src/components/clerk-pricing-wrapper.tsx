'use client'

import { PricingTable } from '@clerk/nextjs'
import { ClerkPricingSkeleton } from '@/components/ui/clerk-pricing-skeleton'
import {
  useState,
  useEffect,
  memo,
  useCallback,
  useMemo,
  Suspense,
} from 'react'
import {
  preloadPricingResources,
  trackPricingPagePerformance,
  pricingStorage,
} from '@/lib/pricing-cache'

// Create a memoized version of PricingTable to prevent unnecessary re-renders
const MemoizedPricingTable = memo(PricingTable)

// Cache for storing the loaded state across navigation
const pricingTableCache = {
  isLoaded: false,
  loadedAt: 0,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes cache
}

// Check if the pricing table is cached and still valid
const isCacheValid = () => {
  const now = Date.now()
  return (
    pricingTableCache.isLoaded &&
    now - pricingTableCache.loadedAt < pricingTableCache.cacheTimeout
  )
}

export function ClerkPricingWrapper() {
  const [isLoading, setIsLoading] = useState(true)
  const [showSkeleton, setShowSkeleton] = useState(true)

  useEffect(() => {
    // Show skeleton for at least 500ms to avoid flashing
    const minLoadingTime = setTimeout(() => {
      setShowSkeleton(false)
    }, 500)

    // Set a maximum loading time of 3 seconds
    const maxLoadingTime = setTimeout(() => {
      setIsLoading(false)
      setShowSkeleton(false)
    }, 3000)

    return () => {
      clearTimeout(minLoadingTime)
      clearTimeout(maxLoadingTime)
    }
  }, [])

  // Listen for when Clerk pricing table is likely loaded
  useEffect(() => {
    const checkForClerkTable = () => {
      // Look for Clerk pricing table elements in the DOM
      const clerkElements =
        document.querySelector('[data-clerk-pricing-table]') ||
        document.querySelector('.cl-pricing-table') ||
        document.querySelector('[class*="clerk"]')

      if (clerkElements) {
        // Add a small delay to ensure content is fully rendered
        setTimeout(() => {
          setIsLoading(false)
        }, 200)
        return true
      }
      return false
    }

    if (isLoading) {
      const interval = setInterval(() => {
        if (checkForClerkTable()) {
          clearInterval(interval)
        }
      }, 100)

      return () => clearInterval(interval)
    }
  }, [isLoading])

  return (
    <div className='relative'>
      {/* Show skeleton while loading */}
      {(isLoading || showSkeleton) && (
        <div className='animate-pulse'>
          <ClerkPricingSkeleton />
        </div>
      )}

      {/* Clerk Pricing Table */}
      <div
        className={`transition-opacity duration-300 ${
          isLoading || showSkeleton
            ? 'opacity-0 absolute inset-0'
            : 'opacity-100'
        }`}
        onLoad={() => {
          setIsLoading(false)
          setShowSkeleton(false)
        }}
      >
        <PricingTable />
      </div>
    </div>
  )
}

// Optimized approach with better performance and caching
export function ClerkPricingWrapperOptimized() {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    let isComponentMounted = true
    let loadingTimer: NodeJS.Timeout

    // Initialize performance tracking
    trackPricingPagePerformance()

    // More efficient loading detection
    const handleClerkLoad = () => {
      if (!isComponentMounted) return

      // Use a shorter, more predictable loading time
      loadingTimer = setTimeout(() => {
        if (isComponentMounted) {
          setIsLoading(false)
        }
      }, 1000) // Further reduced to 1000ms for better UX
    }

    // Error handling for failed loads
    const handleClerkError = () => {
      if (isComponentMounted) {
        setHasError(true)
        setIsLoading(false)
      }
    }

    // Initialize loading sequence with optimizations
    try {
      // Use the optimized preloading function
      preloadPricingResources()
      handleClerkLoad()
    } catch (error) {
      console.warn('Error initializing Clerk pricing:', error)
      handleClerkError()
    }

    return () => {
      isComponentMounted = false
      if (loadingTimer) {
        clearTimeout(loadingTimer)
      }
    }
  }, [])

  // Error state
  if (hasError) {
    return (
      <div className='relative min-h-[500px] sm:min-h-[600px] flex items-center justify-center'>
        <div className='text-center space-y-4'>
          <p className='text-muted-foreground'>
            Unable to load pricing information.
          </p>
          <button
            onClick={() => window.location.reload()}
            className='px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors'
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className='relative min-h-[500px] sm:min-h-[600px]'>
      {/* Optimized skeleton loader */}
      {isLoading && (
        <div className='absolute inset-0'>
          <ClerkPricingSkeleton />
        </div>
      )}

      {/* Actual Clerk pricing table with better loading */}
      <div
        className={`transition-all duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
        style={{
          // Prevent layout shift
          minHeight: isLoading ? '500px' : 'auto',
        }}
      >
        <PricingTable />
      </div>
    </div>
  )
}

// Cached version that persists across navigation
export function ClerkPricingWrapperCached() {
  // Check cache first to avoid showing skeleton on return visits
  const [isLoading, setIsLoading] = useState(() => !isCacheValid())
  const [hasError, setHasError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Memoize the pricing table props to prevent unnecessary re-renders
  const pricingTableProps = useMemo(
    () => ({
      // Add any props you want to pass to PricingTable here
      // appearance: { ... },
      // fallback: <ClerkPricingSkeleton />,
    }),
    []
  )

  const handlePricingLoaded = useCallback(() => {
    // Update cache when pricing table loads
    pricingTableCache.isLoaded = true
    pricingTableCache.loadedAt = Date.now()
    setIsLoading(false)
    setHasError(false)
  }, [])

  const handlePricingError = useCallback(() => {
    setHasError(true)
    setIsLoading(false)
    // Clear cache on error
    pricingTableCache.isLoaded = false
  }, [])

  useEffect(() => {
    let isComponentMounted = true
    let loadingTimer: NodeJS.Timeout

    // If cache is valid, skip loading
    if (isCacheValid()) {
      setIsLoading(false)
      return
    }

    const initializePricing = async () => {
      try {
        // Preload resources
        preloadPricingResources()

        // Track performance
        trackPricingPagePerformance()

        // Shorter loading time since we're caching
        loadingTimer = setTimeout(() => {
          if (isComponentMounted) {
            handlePricingLoaded()
          }
        }, 800) // Reduced loading time
      } catch (error) {
        console.warn('Error initializing pricing:', error)
        if (isComponentMounted && retryCount < 2) {
          setRetryCount(prev => prev + 1)
          setTimeout(() => initializePricing(), 1000)
        } else {
          handlePricingError()
        }
      }
    }

    initializePricing()

    return () => {
      isComponentMounted = false
      if (loadingTimer) clearTimeout(loadingTimer)
    }
  }, [retryCount, handlePricingLoaded, handlePricingError])

  // Enhanced error state with cache clearing
  if (hasError) {
    return (
      <div className='relative min-h-[500px] sm:min-h-[600px] flex items-center justify-center'>
        <div className='text-center space-y-4 max-w-md'>
          <div className='text-muted-foreground space-y-2'>
            <p>Unable to load pricing information.</p>
            {retryCount > 0 && (
              <p className='text-sm'>
                Attempted {retryCount} time{retryCount > 1 ? 's' : ''}
              </p>
            )}
          </div>
          <div className='flex gap-2 justify-center'>
            <button
              onClick={() => {
                // Clear cache and retry
                pricingTableCache.isLoaded = false
                setHasError(false)
                setIsLoading(true)
                setRetryCount(0)
              }}
              className='px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors'
            >
              Retry
            </button>
            <button
              onClick={() => window.location.reload()}
              className='px-4 py-2 border border-border rounded-md hover:bg-muted transition-colors'
            >
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='relative min-h-[500px] sm:min-h-[600px]'>
      {/* Show skeleton only if not cached */}
      {isLoading && (
        <div className='absolute inset-0'>
          <ClerkPricingSkeleton />
        </div>
      )}

      {/* Cached Clerk pricing table */}
      <div
        className={`transition-all duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
        style={{
          minHeight: isLoading ? '500px' : 'auto',
        }}
      >
        <MemoizedPricingTable {...pricingTableProps} />
      </div>
    </div>
  )
}

// Ultimate cached version with Suspense and persistent caching
export function ClerkPricingWrapperUltimate() {
  // Use enhanced storage utilities for better caching
  const [isInitialized, setIsInitialized] = useState(() =>
    pricingStorage.isCached()
  )
  const [hasError, setHasError] = useState(false)

  const handleInitialization = useCallback(() => {
    try {
      // Preload resources
      preloadPricingResources()
      trackPricingPagePerformance()

      // Mark as loaded in storage
      pricingStorage.markLoaded()
      setIsInitialized(true)
      setHasError(false)
    } catch (error) {
      console.warn('Error initializing pricing:', error)
      setHasError(true)
    }
  }, [])

  useEffect(() => {
    if (!isInitialized && !hasError) {
      // Very short delay for smooth UX
      const timer = setTimeout(handleInitialization, 300)
      return () => clearTimeout(timer)
    }
  }, [isInitialized, hasError, handleInitialization])

  // Error state with cache clearing
  if (hasError) {
    return (
      <div className='relative min-h-[500px] sm:min-h-[600px] flex items-center justify-center'>
        <div className='text-center space-y-4'>
          <p className='text-muted-foreground'>
            Unable to load pricing information.
          </p>
          <button
            onClick={() => {
              pricingStorage.clearCache()
              setHasError(false)
              setIsInitialized(false)
            }}
            className='px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors'
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  // Show skeleton only on first load
  if (!isInitialized) {
    return (
      <div className='relative min-h-[500px] sm:min-h-[600px]'>
        <ClerkPricingSkeleton />
      </div>
    )
  }

  // Cached content with Suspense fallback
  return (
    <div className='relative min-h-[500px] sm:min-h-[600px]'>
      <Suspense fallback={<ClerkPricingSkeleton />}>
        <MemoizedPricingTable />
      </Suspense>
    </div>
  )
}

// Development-safe version with minimal DOM interaction
export function ClerkPricingWrapperDev() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simple timer-based approach for development
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000) // Further reduced

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className='relative min-h-[500px] sm:min-h-[600px]'>
      {/* Skeleton loader */}
      {isLoading && (
        <div className='absolute inset-0 animate-pulse'>
          <ClerkPricingSkeleton />
        </div>
      )}

      {/* Actual Clerk pricing table */}
      <div
        className={`transition-all duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
      >
        <MemoizedPricingTable />
      </div>
    </div>
  )
}
