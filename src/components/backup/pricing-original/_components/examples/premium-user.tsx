// 'use client'
// import React from 'react'
// // Update the import path below to the correct location of SubscriptionCard, for example:
// import { SubscriptionCard } from '../SubscriptionCard'
// // Or create the '../_components/index.ts' file and export SubscriptionCard from there if it should exist.

// export default function PremiumUserExample() {
//   // Example of a premium user with an active subscription
//   const userSubscription = {
//     planName: 'Legacy Pro Plan',
//     planPrice: '$29.99/month',
//     status: 'Renewed',
//     renewalDays: 22,
//     promoCode: {
//       code: 'ADORI35OFF',
//       discount: '35%',
//       expiry: 'April 2025',
//       description:
//         "Sign up for any of our Annual Plans during April 2025, and you'll save an extra 35%!",
//     },
//   }

//   return (
//     <div className='max-w-7xl mx-auto py-12 px-4 sm:px-6'>
//       {/* Full subscription details example */}
//       <SubscriptionCard
//         planName={userSubscription.planName}
//         planPrice={userSubscription.planPrice}
//         status={userSubscription.status}
//         renewalDays={userSubscription.renewalDays}
//         promoCode={userSubscription.promoCode}
//       />

//       <div className='mt-8 mb-4'>
//         <h2 className='text-xl font-bold'>
//           Example: Just Subscription Details
//         </h2>
//         <p className='text-muted-foreground'>Without promo code</p>
//       </div>

//       {/* Just subscription details without promo */}
//       <SubscriptionCard
//         planName={userSubscription.planName}
//         planPrice={userSubscription.planPrice}
//         status={userSubscription.status}
//         renewalDays={userSubscription.renewalDays}
//       />

//       <div className='mt-8 mb-4'>
//         <h2 className='text-xl font-bold'>Example: Just Promo Code</h2>
//         <p className='text-muted-foreground'>For free trial users</p>
//       </div>

//       {/* Just promo code for free trial users */}
//       <SubscriptionCard
//         planName='Free Trial'
//         promoCode={userSubscription.promoCode}
//       />
//     </div>
//   )
// }
