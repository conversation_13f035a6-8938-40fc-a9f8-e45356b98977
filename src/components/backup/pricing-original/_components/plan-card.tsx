import React from 'react'
import { Check, X } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Plan } from './types'

interface PlanCardProps {
  plan: Plan
  billingPeriod: 'monthly' | 'annual'
}

const FeatureItem = ({
  children,
  active = true,
}: {
  children: React.ReactNode
  active?: boolean
}) => (
  <li className='flex items-start gap-2.5 py-1.5'>
    {active ? (
      <div className='h-4 w-4 mt-0.5 rounded-full bg-green-100/30 flex items-center justify-center shrink-0'>
        <Check className='h-3 w-3 text-green-500' />
      </div>
    ) : (
      <div className='h-4 w-4 mt-0.5 rounded-full flex items-center justify-center shrink-0'>
        <X className='h-3 w-3 text-muted-foreground' />
      </div>
    )}
    <span className='text-sm'>{children}</span>
  </li>
)

const PlanCard: React.FC<PlanCardProps> = ({ plan, billingPeriod }) => {
  const isPremium = plan.name === 'Premium'
  const isFree = plan.name === 'Free Trial'

  // Format price for display
  const displayPrice =
    billingPeriod === 'annual' && plan.annualPrice
      ? plan.annualPrice
      : plan.price

  return (
    <Card
      className={cn(
        'h-full flex flex-col overflow-hidden border',
        isPremium ? 'border-primary bg-[#161726] text-white' : 'bg-background'
      )}
    >
      <CardContent className='p-0 flex-grow flex flex-col h-full'>
        {/* Plan name & Popular tag */}
        <div className='px-6 pt-6 pb-4 flex items-center justify-between'>
          <h3 className='text-lg font-medium'>{plan.name} plan</h3>
          {plan.highlightTag && (
            <span className='text-xs font-medium bg-green-500 text-white px-2 py-0.5 rounded-full'>
              {plan.highlightTag}
            </span>
          )}
        </div>

        {/* Price */}
        <div className='px-6 pb-4'>
          <div className='flex items-end gap-1'>
            <span className='text-3xl font-bold'>{displayPrice}</span>
            <span
              className={cn(
                'text-sm mb-1',
                isPremium ? 'text-white/70' : 'text-muted-foreground'
              )}
            >
              {!isFree && 'per user'}
              <br />
              {isFree ? plan.billingPeriod : 'per month'}
            </span>
          </div>

          {/* Annual Savings */}
          {billingPeriod === 'annual' && plan.annualSavings && (
            <div
              className={cn(
                'text-sm font-medium mt-1',
                isPremium ? 'text-green-400' : 'text-green-500'
              )}
            >
              {plan.annualSavings}
            </div>
          )}

          <p
            className={cn(
              'text-sm mt-3',
              isPremium ? 'text-white/70' : 'text-muted-foreground'
            )}
          >
            {plan.description}
          </p>
        </div>

        {/* CTA Buttons */}
        <div className='px-6 pb-3 flex flex-col gap-2'>
          <Button
            className={cn(
              'w-full py-5',
              isPremium && 'bg-primary hover:bg-primary/90'
            )}
            variant={plan.buttonVariant}
          >
            {plan.buttonText}
          </Button>
          <Button
            variant='ghost'
            className={cn(
              'w-full py-5 text-sm hover:text-foreground',
              isPremium
                ? 'text-white/70 hover:text-white'
                : 'text-muted-foreground'
            )}
          >
            Chat to sales
          </Button>
        </div>

        {/* Features section */}
        <div
          className={cn(
            'px-6 py-4 border-t flex-grow',
            isPremium ? 'border-white/10' : 'border-border/60'
          )}
        >
          <h4
            className={cn(
              'font-medium text-sm uppercase tracking-wide mb-4',
              isPremium ? 'text-white/60' : 'text-muted-foreground'
            )}
          >
            Features
          </h4>
          <p
            className={cn(
              'text-xs mb-3',
              isPremium ? 'text-white/60' : 'text-muted-foreground'
            )}
          >
            {isPremium
              ? 'Everything in Basic plus...'
              : plan.name === 'Business'
                ? 'Everything in Premium plus...'
                : plan.name === 'Basic'
                  ? 'Basic features for up to 20 users.'
                  : 'Try Adori AI with basic features'}
          </p>

          <ul className='space-y-0'>
            <FeatureItem>
              {plan.videoCreation} video creation
              {plan.videoCreation > 1 ? 's' : ''}
            </FeatureItem>

            <FeatureItem active={plan.videoDownloads > 0}>
              {plan.videoDownloads > 0
                ? `${plan.videoDownloads} video downloads`
                : 'No video downloads'}
            </FeatureItem>

            <FeatureItem>{plan.maxVideoLength} max video length</FeatureItem>

            <FeatureItem>{plan.resolution} resolution</FeatureItem>

            <FeatureItem>{plan.aiImageGenerator}</FeatureItem>

            <FeatureItem active={plan.publishToYoutube}>
              {plan.publishToYoutube
                ? 'YouTube publishing'
                : 'No YouTube publishing'}
            </FeatureItem>

            <FeatureItem active={plan.proVoices}>
              {plan.proVoices ? 'PRO voices' : 'Standard voices only'}
            </FeatureItem>

            <FeatureItem active={plan.voiceCloning}>
              {plan.voiceCloning ? 'Voice cloning' : 'No voice cloning'}
            </FeatureItem>

            {plan.curationSupport && (
              <FeatureItem>{plan.curationSupport} curation support</FeatureItem>
            )}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

export default PlanCard
