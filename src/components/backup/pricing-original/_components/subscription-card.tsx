import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BadgeCheck, Copy } from 'lucide-react'

interface SubscriptionCardProps {
  planName: string
  planPrice?: string
  status?: string
  renewalDays?: number
  promoCode?: {
    code: string
    discount: string
    expiry: string
    description: string
  }
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  planName,
  planPrice,
  status,
  renewalDays,
  promoCode,
}) => {
  const hasPromo = !!promoCode
  const copyCodeToClipboard = () => {
    if (promoCode?.code) {
      navigator.clipboard.writeText(promoCode.code)
    }
  }

  return (
    <Card className='mb-6 overflow-hidden border-border bg-card/50'>
      <CardContent className='p-0 flex flex-col md:flex-row'>
        {/* Subscription details section */}
        <div className='p-6 md:min-w-[280px] lg:min-w-[320px]'>
          <h2 className='text-xl font-semibold mb-5'>Subscription Details</h2>

          <div className='space-y-2'>
            <div>
              <h3 className='text-sm text-muted-foreground'>Current Plan</h3>
              <div className='flex items-center gap-2'>
                <span className='font-medium'>Plan Name:</span> {planName}
                {planPrice && (
                  <span className='text-muted-foreground'>({planPrice})</span>
                )}
              </div>
            </div>

            {status && renewalDays !== undefined && (
              <div className='mt-3'>
                <h3 className='text-sm text-muted-foreground mb-1'>
                  Subscription Status:
                </h3>
                <span className='inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'>
                  <BadgeCheck className='h-3.5 w-3.5' />
                  {status} in {renewalDays} days
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Promo code section */}
        {hasPromo && (
          <div className='bg-secondary/30 p-6 flex-grow relative overflow-hidden'>
            {/* Discount badge */}
            <div className='absolute right-0 top-0 bg-red-600 text-white py-1 px-3 font-bold rounded-bl-lg transform rotate-0'>
              {promoCode.discount} OFF
            </div>

            <div className='max-w-2xl'>
              <h2 className='text-xl font-bold uppercase mb-2 text-gray-900 dark:text-white'>
                Limited Time Offer
              </h2>

              <p className='mb-4 text-gray-800 dark:text-gray-200'>
                {promoCode.description}
              </p>

              <p className='mb-4 text-sm text-gray-700 dark:text-gray-300'>
                Just use below code at checkout to redeem this special offer.
              </p>

              <div className='flex flex-wrap gap-3 items-center'>
                <div className='bg-red-50 dark:bg-red-900/30 py-2 px-4 rounded-md font-mono font-medium'>
                  {promoCode.code}
                </div>

                <Button
                  variant='outline'
                  size='sm'
                  className='h-9 gap-1.5 bg-background/80'
                  onClick={copyCodeToClipboard}
                >
                  <Copy className='h-4 w-4' /> Copy Code
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default SubscriptionCard
