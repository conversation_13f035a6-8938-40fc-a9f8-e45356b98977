# Original Pricing Design Backup

This folder contains the original custom pricing page design components that were replaced with Clerk's billing system.

## Contents

- `page.tsx` - Original pricing page component
- `pricing-modal.tsx` - Original pricing modal component
- `_components/` - All original pricing components
  - `billing-toggle.tsx` - Monthly/Annual toggle
  - `pricing-table.tsx` - Main pricing table component
  - `plan-card.tsx` - Individual plan card component
  - `subscription-card.tsx` - Subscription details card
  - `types.ts` - TypeScript type definitions
  - `index.ts` - Component exports

## Usage

These components can be imported and used if you want to revert to the custom pricing design:

```tsx
import {
  PricingTable,
  BillingToggle,
} from '@/components/backup/pricing-original/_components'
```

## Current State

- **Main pricing page**: Now uses Clerk's `<PricingTable />` component
- **Pricing modal**: Updated to use Clerk's billing system
- **Original design**: Preserved here for future reference

## Features of Original Design

- Beautiful card-based layout
- Monthly/Annual billing toggle
- Premium plan highlighting
- Feature comparison table
- Responsive design
- Dark/light theme support
- Professional hover effects
- Custom styling with Tailwind CSS
