'use client'

import { ChevronsUpDown, LogOut, User } from 'lucide-react'
import { useUser, useClerk } from '@clerk/nextjs'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'

export function NavUser() {
  const { isMobile, state } = useSidebar()
  const isCollapsed = state === 'collapsed'
  const { user } = useUser()
  const { signOut } = useClerk()

  if (!user) {
    return (
      <SidebarMenu>
        <SidebarMenuItem className='flex justify-center'>
          <SidebarMenuButton
            size={isCollapsed ? 'sm' : 'lg'}
            tooltip='User'
            className='flex justify-center'
          >
            <Avatar className='h-8 w-8 rounded-lg'>
              <AvatarFallback className='rounded-lg'>
                <User className='size-4' />
              </AvatarFallback>
            </Avatar>
            <div className='flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden'>
              <span className='truncate font-semibold'>Guest</span>
              <div className='truncate text-xs'>Not signed in</div>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem className='flex justify-center'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size={isCollapsed ? 'sm' : 'lg'}
              tooltip={user.fullName || 'User'}
              className='flex justify-center data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
            >
              <Avatar className='h-8 w-8 rounded-lg'>
                <AvatarImage src={user.imageUrl} alt={user.fullName || ''} />
                <AvatarFallback className='rounded-lg'>
                  {user.firstName?.charAt(0)}
                  {user.lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className='flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden'>
                <span className='truncate font-semibold'>{user.fullName}</span>
                <div className='truncate text-xs'>
                  {user.primaryEmailAddress?.emailAddress}
                </div>
              </div>
              <ChevronsUpDown className='ml-auto size-4 group-data-[collapsible=icon]:hidden' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg'
            side={isMobile ? 'bottom' : 'right'}
            align='end'
            sideOffset={4}
          >
            <DropdownMenuLabel className='p-0 font-normal'>
              <div className='flex items-center gap-2 px-1 py-1.5 text-left text-sm'>
                <Avatar className='h-8 w-8 rounded-lg'>
                  <AvatarImage src={user.imageUrl} alt={user.fullName || ''} />
                  <AvatarFallback className='rounded-lg'>
                    {user.firstName?.charAt(0)}
                    {user.lastName?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className='grid flex-1 text-left text-sm leading-tight'>
                  <span className='truncate font-semibold'>
                    {user.fullName}
                  </span>
                  <span className='truncate text-xs'>
                    {user.primaryEmailAddress?.emailAddress}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {}
              <DropdownMenuItem
                onClick={() => {
                  if (
                    (
                      window as unknown as {
                        Clerk?: { openUserProfile?: () => void }
                      }
                    ).Clerk?.openUserProfile
                  )
                    (
                      window as unknown as {
                        Clerk: { openUserProfile: () => void }
                      }
                    ).Clerk.openUserProfile()
                }}
              >
                <User className='mr-2 size-4' />
                Manage account
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => signOut()}>
              <LogOut className='mr-2 size-4' />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
