'use client'

import { ChevronRight, type LucideIcon } from 'lucide-react'
import Link from 'next/link'
import { useQueryPrefetch } from '@/hooks/use-query-prefetch'
import { useSidebarState } from '@/hooks/use-sidebar-state'
import { useSidebar } from '@/components/ui/sidebar'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      icon?: LucideIcon
    }[]
  }[]
}) {
  const prefetch = useQueryPrefetch()
  const { getSubNavState, toggleSubNav, isInitialized } = useSidebarState()
  const { state: sidebarState, isMobile, setOpenMobile } = useSidebar()
  const isCollapsed = sidebarState === 'collapsed'

  // Handle navigation click - close mobile sidebar if on mobile
  const handleNavClick = () => {
    if (isMobile) {
      setOpenMobile(false)
    }
  }

  const handleHover = (url: string) => {
    // Prefetch based on the URL being hovered
    if (url === '/projects') {
      prefetch.onHoverProjects?.()
    } else if (url === '/my-videos') {
      prefetch.onHoverMyVideos?.()
    } else if (url.includes('/create-video')) {
      prefetch.onHoverCreateVideo?.()
    }
  }

  // Don't render until localStorage state is initialized to prevent hydration mismatch
  if (!isInitialized) {
    return null
  }
  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map(item => {
          // Check if item has sub-items
          if (!item.items || item.items.length === 0) {
            // Render as simple link if no sub-items
            return (
              <SidebarMenuItem key={item.title}>
                <Link href={item.url} prefetch={true} onClick={handleNavClick}>
                  <SidebarMenuButton
                    tooltip={item.title}
                    onMouseEnter={() => handleHover(item.url)}
                  >
                    {item.icon && <item.icon className='size-4' />}
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
            )
          }

          // Render as collapsible with clickable main item and persistent state
          const isSubNavExpanded = getSubNavState(item.title)

          return (
            <Collapsible
              key={item.title}
              asChild
              open={isSubNavExpanded}
              onOpenChange={() => toggleSubNav(item.title)}
              className='group/collapsible'
            >
              <SidebarMenuItem>
                {/* Enhanced mini sidebar UX - show tooltip with sub-items when collapsed */}
                {isCollapsed ? (
                  <TooltipProvider>
                    <Tooltip delayDuration={300}>
                      <TooltipTrigger asChild>
                        <Link
                          href={item.url}
                          prefetch={true}
                          onClick={handleNavClick}
                        >
                          <SidebarMenuButton
                            tooltip={item.title}
                            className='w-full justify-center'
                          >
                            {item.icon && <item.icon className='size-4' />}
                          </SidebarMenuButton>
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent
                        side='right'
                        className='p-0 border-0 bg-transparent shadow-none'
                      >
                        <div className='bg-popover text-popover-foreground border-border border rounded-md shadow-md p-3 min-w-[200px]'>
                          <div className='font-semibold text-sm mb-3 px-2 py-1 border-b border-border text-popover-foreground'>
                            {item.title}
                          </div>
                          <div className='space-y-1'>
                            {item.items?.map(subItem => (
                              <Link
                                key={subItem.title}
                                href={subItem.url}
                                prefetch={true}
                                onClick={handleNavClick}
                              >
                                <div className='flex items-center gap-3 px-2 py-2 text-sm rounded-sm hover:bg-accent hover:text-accent-foreground transition-colors text-popover-foreground'>
                                  {subItem.icon && (
                                    <subItem.icon className='size-4 text-muted-foreground' />
                                  )}
                                  <span className='font-medium'>
                                    {subItem.title}
                                  </span>
                                </div>
                              </Link>
                            ))}
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  // Expanded sidebar - normal collapsible behavior
                  <>
                    <div className='flex items-center w-full'>
                      <Link
                        href={item.url}
                        className='flex-1'
                        prefetch={true}
                        onClick={handleNavClick}
                      >
                        <SidebarMenuButton
                          tooltip={item.title}
                          className='w-full justify-start'
                        >
                          {item.icon && <item.icon className='size-4' />}
                          <span>{item.title}</span>
                        </SidebarMenuButton>
                      </Link>
                      <CollapsibleTrigger asChild>
                        <div className='flex items-center justify-center w-6 h-6 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-sm cursor-pointer'>
                          <ChevronRight className='size-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                        </div>
                      </CollapsibleTrigger>
                    </div>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.items?.map(subItem => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <Link
                              href={subItem.url}
                              prefetch={true}
                              onClick={handleNavClick}
                            >
                              <SidebarMenuSubButton>
                                {subItem.icon && (
                                  <subItem.icon className='size-4 mr-2 !text-gray-700 dark:!text-gray-300' />
                                )}
                                <span>{subItem.title}</span>
                              </SidebarMenuSubButton>
                            </Link>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </>
                )}
              </SidebarMenuItem>
            </Collapsible>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
