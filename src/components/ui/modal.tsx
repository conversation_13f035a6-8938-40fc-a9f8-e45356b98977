'use client'

import { useState, useEffect } from 'react'
import { useStableMediaQuery } from '@/hooks/use-stable-media-query'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'

import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerFooter,
  DrawerClose,
} from '@/components/ui/drawer'

import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { VisuallyHidden } from '@/components/ui/visually-hidden'

interface ModalProps {
  title?: string
  description?: string
  isOpen: boolean
  onClose: () => void
  children?: React.ReactNode
  className?: string
  showCloseButton?: boolean
  footer?: React.ReactNode
  type?: string
  size?:
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
}

export function Modal({
  title,
  description,
  isOpen,
  onClose,
  children,
  className,
  showCloseButton = true,
  footer,
  type,
  size = 'md',
}: ModalProps) {
  // Use stable media query to determine if we should use Dialog or Drawer
  const isDesktop = useStableMediaQuery('(min-width: 768px)')
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) return null

  // Get size class based on size prop or type
  const getSizeClass = () => {
    if (className?.includes('max-w-')) return ''

    // If type is pricing, use larger size
    if (type === 'pricing') return 'sm:max-w-7xl w-[95vw]'

    // Otherwise use size prop
    return {
      sm: 'sm:max-w-sm',
      md: 'sm:max-w-md',
      lg: 'sm:max-w-lg',
      xl: 'sm:max-w-xl',
      '2xl': 'sm:max-w-2xl',
      '3xl': 'sm:max-w-3xl',
      '4xl': 'sm:max-w-4xl',
      '5xl': 'sm:max-w-5xl',
      '6xl': 'sm:max-w-6xl',
      '7xl': 'sm:max-w-7xl',
    }[size]
  }

  // Get content padding based on type
  const getContentPadding = () => (type === 'pricing' ? 'pt-2 px-6' : 'px-6')

  const resolvedTitle = title || 'Dialog'

  // Shared content between Dialog and Drawer
  const modalContent = (
    <>
      <div className={cn('mb-4', type === 'pricing' ? 'pt-6 px-6' : 'pt-6')}>
        {isDesktop ? (
          <DialogHeader>
            {title ? (
              <DialogTitle>{title}</DialogTitle>
            ) : (
              <VisuallyHidden>
                <DialogTitle>{resolvedTitle}</DialogTitle>
              </VisuallyHidden>
            )}
            {description && (
              <DialogDescription>{description}</DialogDescription>
            )}
          </DialogHeader>
        ) : (
          <DrawerHeader>
            {title ? (
              <DrawerTitle>{title}</DrawerTitle>
            ) : (
              <VisuallyHidden>
                <DrawerTitle>{resolvedTitle}</DrawerTitle>
              </VisuallyHidden>
            )}
            {description && (
              <DrawerDescription>{description}</DrawerDescription>
            )}
          </DrawerHeader>
        )}
      </div>

      <div
        className={cn(
          'overflow-y-auto flex-1',
          '[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]',
          getContentPadding(),
          className
        )}
      >
        {children}
      </div>

      {footer && (
        <div className='mt-6'>
          {isDesktop ? (
            <DialogFooter>{footer}</DialogFooter>
          ) : (
            <DrawerFooter>{footer}</DrawerFooter>
          )}
        </div>
      )}

      {showCloseButton && !isDesktop && (
        <DrawerClose asChild>
          <Button
            variant='outline'
            size='icon'
            className='absolute right-4 top-4'
            onClick={onClose}
          >
            <X className='h-4 w-4' />
            <span className='sr-only'>Close</span>
          </Button>
        </DrawerClose>
      )}
    </>
  )

  // Render Dialog on desktop and Drawer on mobile
  return isDesktop ? (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          'max-h-[90vh] p-0 flex flex-col overflow-hidden',
          getSizeClass(),
          className
        )}
      >
        {modalContent}
      </DialogContent>
    </Dialog>
  ) : (
    <Drawer open={isOpen} onOpenChange={onClose} shouldScaleBackground>
      <DrawerContent className='px-0 h-[85vh] max-h-[100vh] overflow-hidden'>
        {modalContent}
      </DrawerContent>
    </Drawer>
  )
}
