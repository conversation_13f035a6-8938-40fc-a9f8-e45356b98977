import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { VisuallyHidden } from '@/components/ui/visually-hidden'

interface LoaderDialogProps {
  open: boolean
  title?: string
  subtitle?: string
}

export function LoaderDialog({
  open,
  title = 'Processing',
  subtitle = 'Please wait...',
}: LoaderDialogProps) {
  return (
    <Dialog open={open}>
      <DialogContent className='sm:max-w-md border-none shadow-2xl [&>button]:hidden bg-background'>
        <VisuallyHidden>
          <DialogTitle>{title}</DialogTitle>
        </VisuallyHidden>
        <div className='flex flex-col items-center justify-between py-12 px-2'>
          {/* CSS Loader Animation - Document to Audio Conversion */}
          <div className='relative mb-8'>
            <div className='flex items-center justify-center gap-8'>
              {/* Document Icon */}
              <span className='loader'></span>

              {/* Arrow */}
              <div className='arrow'>
                <svg width='24' height='24' viewBox='0 0 24 24' fill='none'>
                  <path
                    d='M5 12h14m-7-7l7 7-7 7'
                    stroke='currentColor'
                    strokeWidth='2'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  />
                </svg>
              </div>

              {/* Audio Bars */}
              <span className='audio-bars'></span>
            </div>
          </div>

          {/* Text Content */}
          <div className='text-center space-y-3'>
            <h3 className='text-xl font-semibold text-foreground'>{title}</h3>
            <p className='text-sm text-muted-foreground max-w-xs'>{subtitle}</p>
          </div>
        </div>

        <style jsx>{`
          .conversion-loader {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            height: 60px;
          }

          .loader {
            position: relative;
            width: 100px;
            height: 130px;
            background: oklch(1 0 0);
            border-radius: 4px;
            border: 1px solid oklch(0.87 0 0);
          }
          .loader:before {
            content: '';
            position: absolute;
            width: 54px;
            height: 25px;
            left: 50%;
            top: 0;
            background-image:
              radial-gradient(
                ellipse at center,
                transparent 24%,
                oklch(0.62 0.12 287.16) 25%,
                oklch(0.62 0.12 287.16) 64%,
                transparent 65%
              ),
              linear-gradient(
                to bottom,
                transparent 34%,
                oklch(0.62 0.12 287.16) 35%
              );
            background-size:
              12px 12px,
              100% auto;
            background-repeat: no-repeat;
            background-position: center top;
            transform: translate(-50%, -65%);
            box-shadow: 0 -3px oklch(0.39 0 0 / 0.25) inset;
          }
          .loader:after {
            content: '';
            position: absolute;
            left: 50%;
            top: 20%;
            transform: translateX(-50%);
            width: 66%;
            height: 60%;
            background: linear-gradient(
              to bottom,
              oklch(0.83 0.04 249.44) 30%,
              transparent 31%
            );
            background-size: 100% 16px;
            animation: writeDown 2s ease-out infinite;
          }

          @keyframes writeDown {
            0% {
              height: 0%;
              opacity: 0;
            }
            20% {
              height: 0%;
              opacity: 1;
            }
            80% {
              height: 65%;
              opacity: 1;
            }
            100% {
              height: 65%;
              opacity: 0;
            }
          }

          .arrow {
            color: oklch(0.62 0.12 287.16);
            animation: bounce 1.5s ease-in-out infinite;
          }

          .audio-bars {
            position: relative;
            width: 85px;
            height: 50px;
            background-repeat: no-repeat;
            background-image:
              linear-gradient(oklch(0.62 0.12 287.16) 50px, transparent 0),
              linear-gradient(oklch(0.62 0.12 287.16) 50px, transparent 0),
              linear-gradient(oklch(0.62 0.12 287.16) 50px, transparent 0),
              linear-gradient(oklch(0.62 0.12 287.16) 50px, transparent 0),
              linear-gradient(oklch(0.62 0.12 287.16) 50px, transparent 0),
              linear-gradient(oklch(0.62 0.12 287.16) 50px, transparent 0);
            background-position:
              0px center,
              15px center,
              30px center,
              45px center,
              60px center,
              75px center,
              90px center;
            animation: rikSpikeRoll 0.65s linear infinite alternate;
          }
          @keyframes rikSpikeRoll {
            0% {
              background-size: 10px 3px;
            }
            16% {
              background-size:
                10px 50px,
                10px 3px,
                10px 3px,
                10px 3px,
                10px 3px,
                10px 3px;
            }
            33% {
              background-size:
                10px 30px,
                10px 50px,
                10px 3px,
                10px 3px,
                10px 3px,
                10px 3px;
            }
            50% {
              background-size:
                10px 10px,
                10px 30px,
                10px 50px,
                10px 3px,
                10px 3px,
                10px 3px;
            }
            66% {
              background-size:
                10px 3px,
                10px 10px,
                10px 30px,
                10px 50px,
                10px 3px,
                10px 3px;
            }
            83% {
              background-size:
                10px 3px,
                10px 3px,
                10px 10px,
                10px 30px,
                10px 50px,
                10px 3px;
            }
            100% {
              background-size:
                10px 3px,
                10px 3px,
                10px 3px,
                10px 10px,
                10px 30px,
                10px 50px;
            }
          }

          /* Dark mode adjustments */
          @media (prefers-color-scheme: dark) {
            .loader {
              background: oklch(0.32 0.01 236.69);
              border-color: oklch(0.39 0 0);
            }
            .loader:before {
              box-shadow: 0 -3px oklch(0.23 0.01 258.36 / 0.4) inset;
            }
          }

          @keyframes bounce {
            0%,
            100% {
              transform: translateX(0);
            }
            50% {
              transform: translateX(4px);
            }
          }
        `}</style>
      </DialogContent>
    </Dialog>
  )
}
