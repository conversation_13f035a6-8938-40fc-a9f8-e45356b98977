'use client'

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import { dark } from '@clerk/themes'
import { useTheme } from 'next-themes'
import { ReactNode } from 'react'

interface ClerkThemeProviderProps {
  children: ReactNode
}

export function ClerkThemeProvider({ children }: ClerkThemeProviderProps) {
  const { resolvedTheme } = useTheme()

  return (
    <ClerkProvider
      appearance={{
        baseTheme: resolvedTheme === 'dark' ? dark : undefined,
        variables:
          resolvedTheme === 'dark'
            ? {
                // 🌙 Dark theme variables (match your `.dark`)
                colorPrimary: 'oklch(0.8493 0.2073 128.8497)', // --primary
                colorBackground: 'oklch(0.2919 0.0283 152.6613)', // --background
                colorText: 'oklch(0.9423 0.0097 72.6595)', // --foreground
                colorInputBackground: 'oklch(0.3942 0.0265 142.9926)', // --input
                colorInputText: 'oklch(0.9423 0.0097 72.6595)', // --input-foreground
                colorDanger: 'oklch(0.5386 0.1937 26.7249)', // --destructive
                colorSuccess: 'oklch(0.6731 0.1624 144.2083)', // can be your --ring
              }
            : {
                // ☀️ Light theme variables (match your `:root`)
                colorPrimary: 'oklch(0.8493 0.2073 128.8497)', // --primary
                colorBackground: 'oklch(0.9711 0.0074 80.7211)', // --background
                colorText: 'oklch(0.3 0.0358 30.2042)', // --foreground
                colorInputBackground: 'oklch(0.8805 0.0208 74.6428)', // --input
                colorInputText: 'oklch(0.3 0.0358 30.2042)', // --input-foreground
                colorDanger: 'oklch(0.5386 0.1937 26.7249)', // --destructive
                colorSuccess: 'oklch(0.5234 0.1347 144.1672)', // --ring
              },
      }}
      afterSignOutUrl='/'
    >
      {children}
    </ClerkProvider>
  )
}
