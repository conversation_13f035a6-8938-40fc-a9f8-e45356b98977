'use client'

import { PricingTable } from '@clerk/nextjs'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function PricingModal(_props?: Record<string, unknown>) {
  return (
    <div className='max-w-4xl mx-auto py-6 px-2 sm:px-4'>
      {/* Page header */}
      <div className='mb-8 text-center'>
        <h1 className='text-2xl font-bold mb-2'>Choose your plan</h1>
        <p className='text-muted-foreground max-w-2xl mx-auto text-sm'>
          Select the perfect plan for your content creation needs. All plans
          include access to our AI video creation tools.
        </p>
      </div>

      {/* Clerk Pricing Table */}
      <div className='mb-12'>
        <PricingTable
          checkoutProps={{
            appearance: {
              // Style the checkout to match your theme
            },
          }}
        />
      </div>

      {/* Enterprise Contact Section */}
      <div className='w-full mx-auto rounded-lg bg-card border border-border/40 p-6 text-center'>
        <div className='max-w-lg mx-auto'>
          <h2 className='text-lg font-semibold mb-2'>
            Need a custom solution?
          </h2>
          <p className='text-muted-foreground mb-4 text-sm'>
            Our team can help you find the perfect plan for your content
            creation needs. We offer custom solutions for enterprise customers
            with special requirements.
          </p>
          <a
            href='mailto:<EMAIL>'
            className='inline-flex items-center gap-1.5 text-primary hover:text-primary/90 font-medium'
          >
            Contact our sales team
            <svg
              className='h-4 w-4'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 5l7 7-7 7'
              />
            </svg>
          </a>
        </div>
      </div>
    </div>
  )
}
