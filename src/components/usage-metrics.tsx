'use client'

import * as React from 'react'
import {
  ChevronDown,
  BarChart3,
  Video,
  Image as ImageIcon,
  TrendingUp,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  SidebarMenuButton,
  SidebarMenu,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { UpgradeButton } from '@/components/upgrade-button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'

export interface UsageMetric {
  id: string
  name: string
  max: number
  used: number
  icon: React.ReactNode
  color: string
}

interface UsageMetricsProps {
  className?: string
  isCollapsed?: boolean
}

const usageData: UsageMetric[] = [
  {
    id: 'projects',
    name: 'Projects',
    max: 120,
    used: 120,
    icon: <BarChart3 className='h-4 w-4' />,
    color: 'from-blue-500 to-blue-600',
  },
  {
    id: 'exports',
    name: 'Video Exports',
    max: 3,
    used: 1,
    icon: <Video className='h-4 w-4' />,
    color: 'from-green-500 to-green-600',
  },
  {
    id: 'images',
    name: 'AI Images',
    max: 100,
    used: 50,
    icon: <ImageIcon className='h-4 w-4' />,
    color: 'from-purple-500 to-purple-600',
  },
]

export function UsageMetrics({ className, isCollapsed }: UsageMetricsProps) {
  const [selectedMetric, setSelectedMetric] = React.useState<UsageMetric>(
    usageData[0]
  )
  const [showAll] = React.useState(true)
  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false)

  // Calculate usage percentage
  const getUsagePercentage = (used: number, max: number) => {
    return Math.min((used / max) * 100, 100)
  }

  if (isCollapsed) {
    return (
      <SidebarMenu className={className}>
        <SidebarMenuItem>
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                tooltip='Usage Overview'
                className='flex justify-center'
              >
                <TrendingUp className='size-4' />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='start' side='right' className='w-72'>
              <DropdownMenuLabel className='flex items-center gap-2'>
                <TrendingUp className='h-4 w-4' />
                Usage Overview
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className='p-3 space-y-4'>
                {usageData.map(metric => (
                  <div key={metric.id} className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        {metric.icon}
                        <span className='text-sm font-medium'>
                          {metric.name}
                        </span>
                      </div>
                      <Badge
                        variant={
                          getUsagePercentage(metric.used, metric.max) >= 90
                            ? 'destructive'
                            : getUsagePercentage(metric.used, metric.max) >= 70
                              ? 'secondary'
                              : 'default'
                        }
                        className='text-xs px-2 py-0.5'
                      >
                        {metric.used}/{metric.max}
                      </Badge>
                    </div>
                    <Progress
                      value={getUsagePercentage(metric.used, metric.max)}
                      className='h-2'
                    />
                  </div>
                ))}
              </div>
              <DropdownMenuSeparator />
              <div className='p-3'>
                <UpgradeButton className='w-full' size='sm' />
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return (
    <div className={cn('px-3 py-4 border-t', className)}>
      {/* Header with toggle */}
      <div className='flex items-center justify-between mb-4'>
        <div className='flex items-center gap-2'>
          <TrendingUp className='h-4 w-4 text-muted-foreground' />
          <h4 className='font-semibold text-sm'>Usage</h4>
        </div>
        {/* <Button
          variant='ghost'
          size='sm'
          className='h-7 px-2 text-xs hover:bg-accent/50 transition-colors'
          onClick={() => setShowAll(!showAll)}
        >
          {showAll ? 'Collapse' : 'View All'}
          {showAll ? (
            <ChevronUp className='h-3 w-3 ml-1.5' />
          ) : (
            <ChevronDown className='h-3 w-3 ml-1.5' />
          )}
        </Button> */}
      </div>

      {showAll ? (
        /* Show all metrics */
        <div className='space-y-4 mb-4'>
          {usageData.map(metric => (
            <div key={metric.id} className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2'>
                  {metric.icon}
                  <span className='text-sm font-medium'>{metric.name}</span>
                </div>
                <Badge
                  variant={
                    getUsagePercentage(metric.used, metric.max) >= 90
                      ? 'destructive'
                      : getUsagePercentage(metric.used, metric.max) >= 70
                        ? 'secondary'
                        : 'default'
                  }
                  className='text-xs px-2 py-0.5'
                >
                  {metric.used}/{metric.max}
                </Badge>
              </div>
              <Progress
                value={getUsagePercentage(metric.used, metric.max)}
                className='h-2'
              />
            </div>
          ))}
        </div>
      ) : (
        /* Show single metric with dropdown */
        <div className='space-y-3 mb-4'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='ghost'
                className='w-full justify-between p-2 h-auto hover:bg-accent/50 rounded-md border-0 transition-colors'
              >
                <div className='flex items-center gap-3 text-left'>
                  <div className='p-1.5 rounded-md bg-muted/50'>
                    {selectedMetric.icon}
                  </div>
                  <div className='flex-1'>
                    <div className='text-sm font-medium'>
                      {selectedMetric.name}
                    </div>
                    <div className='text-xs text-muted-foreground'>
                      {selectedMetric.used} of {selectedMetric.max} used
                    </div>
                  </div>
                </div>
                <ChevronDown className='h-4 w-4 text-muted-foreground' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='w-56'>
              {usageData.map(metric => (
                <DropdownMenuItem
                  key={metric.id}
                  onClick={() => setSelectedMetric(metric)}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-2'>
                    {metric.icon}
                    <span>{metric.name}</span>
                  </div>
                  <Badge variant='outline' className='text-xs'>
                    {metric.used}/{metric.max}
                  </Badge>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Progress bar for selected metric - no bottom numbers */}
          <div className='space-y-2'>
            <Progress
              value={getUsagePercentage(
                selectedMetric.used,
                selectedMetric.max
              )}
              className='h-2'
            />
          </div>
        </div>
      )}

      {/* Upgrade button */}
      <UpgradeButton className='w-full' size='sm' />
    </div>
  )
}
